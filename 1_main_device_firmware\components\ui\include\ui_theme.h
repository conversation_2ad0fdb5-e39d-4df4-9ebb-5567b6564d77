/**
 * @file ui_theme.h
 * @brief TIMO UI主题系统头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef UI_THEME_H
#define UI_THEME_H

#include "esp_err.h"
#include "lvgl.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 主题ID定义 */
typedef enum {
    UI_THEME_DEFAULT = 0,       // 默认主题
    UI_THEME_DARK,              // 深色主题
    UI_THEME_BLUE,              // 蓝色主题
    UI_THEME_GREEN,             // 绿色主题
    UI_THEME_ORANGE,            // 橙色主题
    UI_THEME_PURPLE,            // 紫色主题
    UI_THEME_MAX
} ui_theme_id_t;

/* 主题颜色定义 */
typedef struct {
    lv_color_t primary;         // 主色调
    lv_color_t secondary;       // 次色调
    lv_color_t background;      // 背景色
    lv_color_t surface;         // 表面色
    lv_color_t text_primary;    // 主文本色
    lv_color_t text_secondary;  // 次文本色
    lv_color_t accent;          // 强调色
    lv_color_t error;           // 错误色
    lv_color_t warning;         // 警告色
    lv_color_t success;         // 成功色
} ui_theme_colors_t;

/* 主题信息结构体 */
typedef struct {
    ui_theme_id_t id;
    const char *name;
    const char *description;
    ui_theme_colors_t colors;
    bool is_dark;
} ui_theme_info_t;

/**
 * @brief 初始化UI主题系统
 * @return esp_err_t 
 */
esp_err_t ui_theme_init(void);

/**
 * @brief 应用指定主题
 * @param theme_id 主题ID
 * @return esp_err_t 
 */
esp_err_t ui_theme_apply(ui_theme_id_t theme_id);

/**
 * @brief 获取当前主题ID
 * @return ui_theme_id_t 
 */
ui_theme_id_t ui_theme_get_current(void);

/**
 * @brief 获取主题信息
 * @param theme_id 主题ID
 * @return const ui_theme_info_t* 
 */
const ui_theme_info_t* ui_theme_get_info(ui_theme_id_t theme_id);

/**
 * @brief 获取主题数量
 * @return uint8_t 
 */
uint8_t ui_theme_get_count(void);

/**
 * @brief 获取主题颜色
 * @param theme_id 主题ID
 * @return const ui_theme_colors_t* 
 */
const ui_theme_colors_t* ui_theme_get_colors(ui_theme_id_t theme_id);

/**
 * @brief 创建圆形样式
 * @param radius 圆角半径
 * @return lv_style_t* 
 */
lv_style_t* ui_theme_create_circle_style(lv_coord_t radius);

/**
 * @brief 创建按钮样式
 * @return lv_style_t* 
 */
lv_style_t* ui_theme_create_button_style(void);

/**
 * @brief 创建标签样式
 * @param font_size 字体大小
 * @return lv_style_t* 
 */
lv_style_t* ui_theme_create_label_style(uint8_t font_size);

#ifdef __cplusplus
}
#endif

#endif /* UI_THEME_H */
