/**
 * @file bluetooth_system.c
 * @brief TIMO主体设备蓝牙通信系统实现
 * @version 1.0.0
 * @date 2025-06-28
 */

#include "bluetooth_system.h"
#include "bt_client.h"
#include "bt_protocol.h"
#include "esp_log.h"
#include "esp_bt.h"
#include "esp_bt_main.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include <string.h>

static const char *TAG = "BT_SYSTEM";

/* 蓝牙系统状态 */
static bool g_bt_system_initialized = false;
static bool g_bt_system_running = false;
static bt_connection_state_t g_bt_state = BT_STATE_IDLE;
static bt_connection_info_t g_connection_info = {0};

/* 回调函数 */
static bt_event_callback_t g_event_callback = NULL;
static bt_data_callback_t g_data_callback = NULL;

/* 任务和队列 */
static TaskHandle_t g_bt_task_handle = NULL;
static QueueHandle_t g_bt_event_queue = NULL;
static QueueHandle_t g_bt_data_queue = NULL;
static SemaphoreHandle_t g_bt_mutex = NULL;

/* 扫描结果 */
static bt_device_info_t g_scan_results[10];
static uint8_t g_scan_count = 0;

/* 内部事件结构 */
typedef struct {
    bt_event_type_t type;
    void *data;
    size_t data_size;
} bt_internal_event_t;

/**
 * @brief 发送内部事件
 */
static esp_err_t send_internal_event(bt_event_type_t type, void *data, size_t data_size)
{
    if (!g_bt_event_queue) {
        return ESP_ERR_INVALID_STATE;
    }
    
    bt_internal_event_t event = {
        .type = type,
        .data = NULL,
        .data_size = data_size
    };
    
    if (data && data_size > 0) {
        event.data = malloc(data_size);
        if (event.data) {
            memcpy(event.data, data, data_size);
        }
    }
    
    BaseType_t ret = xQueueSend(g_bt_event_queue, &event, pdMS_TO_TICKS(100));
    if (ret != pdTRUE) {
        if (event.data) {
            free(event.data);
        }
        return ESP_ERR_TIMEOUT;
    }
    
    return ESP_OK;
}

/**
 * @brief 客户端事件回调
 */
static void bt_client_event_handler(bt_event_type_t event, void *data)
{
    ESP_LOGI(TAG, "客户端事件: %d", event);
    
    switch (event) {
        case BT_EVENT_SCAN_START:
            g_bt_state = BT_STATE_SCANNING;
            break;
            
        case BT_EVENT_SCAN_RESULT:
            if (data) {
                bt_device_info_t *device = (bt_device_info_t*)data;
                // 检查是否为目标设备
                if (strstr(device->name, BT_BASE_DEVICE_NAME) != NULL) {
                    device->is_target = true;
                    ESP_LOGI(TAG, "发现目标设备: %s, RSSI: %d", device->name, device->rssi);
                }
                
                // 添加到扫描结果
                if (g_scan_count < sizeof(g_scan_results)/sizeof(g_scan_results[0])) {
                    memcpy(&g_scan_results[g_scan_count], device, sizeof(bt_device_info_t));
                    g_scan_count++;
                }
            }
            break;
            
        case BT_EVENT_SCAN_COMPLETE:
            ESP_LOGI(TAG, "扫描完成，发现 %d 个设备", g_scan_count);
            break;
            
        case BT_EVENT_CONNECT_START:
            g_bt_state = BT_STATE_CONNECTING;
            break;
            
        case BT_EVENT_CONNECTED:
            g_bt_state = BT_STATE_CONNECTED;
            if (data) {
                memcpy(&g_connection_info, data, sizeof(bt_connection_info_t));
            }
            ESP_LOGI(TAG, "已连接到底座设备");
            break;
            
        case BT_EVENT_DISCONNECTED:
            g_bt_state = BT_STATE_DISCONNECTED;
            memset(&g_connection_info, 0, sizeof(bt_connection_info_t));
            ESP_LOGI(TAG, "与底座设备断开连接");
            break;
            
        case BT_EVENT_ERROR:
            g_bt_state = BT_STATE_ERROR;
            ESP_LOGE(TAG, "蓝牙客户端错误");
            break;
            
        default:
            break;
    }
    
    // 转发事件
    send_internal_event(event, data, data ? sizeof(bt_device_info_t) : 0);
}

/**
 * @brief 客户端数据接收回调
 */
static void bt_client_data_handler(const uint8_t *data, uint16_t length)
{
    ESP_LOGI(TAG, "收到数据: %d bytes", length);
    
    if (!data || length == 0) {
        return;
    }
    
    // 解析协议数据包
    bt_packet_header_t header;
    const uint8_t *payload;
    uint16_t payload_len;
    
    if (bt_protocol_parse_packet(data, length, &header, &payload, &payload_len)) {
        // 创建数据包结构
        bt_data_packet_t packet = {0};
        packet.type = header.type;
        packet.cmd = header.cmd;
        packet.length = payload_len;
        packet.timestamp = esp_timer_get_time() / 1000;
        
        if (payload_len > 0 && payload_len <= sizeof(packet.data)) {
            memcpy(packet.data, payload, payload_len);
        }
        
        // 发送到数据队列
        if (g_bt_data_queue) {
            xQueueSend(g_bt_data_queue, &packet, 0);
        }
        
        // 调用回调函数
        if (g_data_callback) {
            g_data_callback(&packet);
        }
    } else {
        ESP_LOGW(TAG, "数据包解析失败");
    }
}

/**
 * @brief 蓝牙系统主任务
 */
static void bluetooth_system_task(void *pvParameters)
{
    ESP_LOGI(TAG, "蓝牙系统任务启动");
    
    bt_internal_event_t event;
    bt_data_packet_t data_packet;
    
    while (g_bt_system_running) {
        // 处理事件
        if (xQueueReceive(g_bt_event_queue, &event, pdMS_TO_TICKS(10)) == pdTRUE) {
            // 调用事件回调
            if (g_event_callback) {
                g_event_callback(event.type, event.data);
            }
            
            // 释放事件数据
            if (event.data) {
                free(event.data);
            }
        }
        
        // 处理数据包
        if (xQueueReceive(g_bt_data_queue, &data_packet, 0) == pdTRUE) {
            ESP_LOGI(TAG, "处理数据包: type=0x%02X, cmd=0x%02X, len=%d", 
                     data_packet.type, data_packet.cmd, data_packet.length);
            
            // 这里可以添加具体的数据包处理逻辑
            switch (data_packet.cmd) {
                case BT_CMD_PONG:
                    ESP_LOGI(TAG, "收到心跳响应");
                    break;
                    
                case BT_CMD_GET_STATUS:
                    ESP_LOGI(TAG, "收到状态响应");
                    break;
                    
                default:
                    ESP_LOGW(TAG, "未处理的命令: 0x%02X", data_packet.cmd);
                    break;
            }
        }
        
        // 定期发送心跳包
        static uint32_t last_ping_time = 0;
        uint32_t current_time = esp_timer_get_time() / 1000;
        if (g_bt_state == BT_STATE_CONNECTED && 
            (current_time - last_ping_time) > 30000) { // 30秒
            bluetooth_system_send_command(BT_CMD_PING, NULL, 0);
            last_ping_time = current_time;
        }
    }
    
    ESP_LOGI(TAG, "蓝牙系统任务结束");
    vTaskDelete(NULL);
}

/**
 * @brief 初始化蓝牙系统
 */
esp_err_t bluetooth_system_init(void)
{
    if (g_bt_system_initialized) {
        ESP_LOGW(TAG, "蓝牙系统已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化蓝牙系统...");
    
    // 创建互斥锁
    g_bt_mutex = xSemaphoreCreateMutex();
    if (!g_bt_mutex) {
        ESP_LOGE(TAG, "创建互斥锁失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建事件队列
    g_bt_event_queue = xQueueCreate(10, sizeof(bt_internal_event_t));
    if (!g_bt_event_queue) {
        ESP_LOGE(TAG, "创建事件队列失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建数据队列
    g_bt_data_queue = xQueueCreate(5, sizeof(bt_data_packet_t));
    if (!g_bt_data_queue) {
        ESP_LOGE(TAG, "创建数据队列失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 初始化BLE客户端
    esp_err_t ret = bt_client_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "BLE客户端初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 注册客户端回调
    bt_client_register_event_callback(bt_client_event_handler);
    bt_client_register_data_callback(bt_client_data_handler);
    
    // 初始化状态
    g_bt_state = BT_STATE_IDLE;
    memset(&g_connection_info, 0, sizeof(bt_connection_info_t));
    g_scan_count = 0;
    
    g_bt_system_initialized = true;
    ESP_LOGI(TAG, "蓝牙系统初始化完成");

    return ESP_OK;
}

/**
 * @brief 启动蓝牙系统
 */
esp_err_t bluetooth_system_start(void)
{
    if (!g_bt_system_initialized) {
        ESP_LOGE(TAG, "蓝牙系统未初始化");
        return ESP_ERR_INVALID_STATE;
    }

    if (g_bt_system_running) {
        ESP_LOGW(TAG, "蓝牙系统已启动");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "启动蓝牙系统...");

    // 创建主任务
    BaseType_t ret = xTaskCreate(
        bluetooth_system_task,
        "bt_system",
        4096,
        NULL,
        5,
        &g_bt_task_handle
    );

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建蓝牙系统任务失败");
        return ESP_ERR_NO_MEM;
    }

    g_bt_system_running = true;
    ESP_LOGI(TAG, "蓝牙系统启动完成");

    return ESP_OK;
}

/**
 * @brief 停止蓝牙系统
 */
esp_err_t bluetooth_system_stop(void)
{
    if (!g_bt_system_running) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "停止蓝牙系统...");

    // 断开连接
    if (g_bt_state == BT_STATE_CONNECTED) {
        bluetooth_system_disconnect();
    }

    // 停止扫描
    if (g_bt_state == BT_STATE_SCANNING) {
        bluetooth_system_stop_scan();
    }

    g_bt_system_running = false;

    // 等待任务结束
    if (g_bt_task_handle) {
        vTaskDelete(g_bt_task_handle);
        g_bt_task_handle = NULL;
    }

    ESP_LOGI(TAG, "蓝牙系统已停止");
    return ESP_OK;
}

/**
 * @brief 反初始化蓝牙系统
 */
esp_err_t bluetooth_system_deinit(void)
{
    if (!g_bt_system_initialized) {
        return ESP_OK;
    }

    // 停止系统
    bluetooth_system_stop();

    // 反初始化客户端
    bt_client_deinit();

    // 清理资源
    if (g_bt_event_queue) {
        vQueueDelete(g_bt_event_queue);
        g_bt_event_queue = NULL;
    }

    if (g_bt_data_queue) {
        vQueueDelete(g_bt_data_queue);
        g_bt_data_queue = NULL;
    }

    if (g_bt_mutex) {
        vSemaphoreDelete(g_bt_mutex);
        g_bt_mutex = NULL;
    }

    g_bt_system_initialized = false;
    ESP_LOGI(TAG, "蓝牙系统反初始化完成");

    return ESP_OK;
}

/**
 * @brief 开始扫描底座设备
 */
esp_err_t bluetooth_system_start_scan(uint32_t timeout_s)
{
    if (!g_bt_system_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (g_bt_state == BT_STATE_SCANNING) {
        ESP_LOGW(TAG, "正在扫描中");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "开始扫描底座设备，超时: %d 秒", timeout_s);

    // 清除之前的扫描结果
    g_scan_count = 0;
    memset(g_scan_results, 0, sizeof(g_scan_results));

    return bt_client_start_scan(timeout_s);
}

/**
 * @brief 停止扫描
 */
esp_err_t bluetooth_system_stop_scan(void)
{
    if (g_bt_state != BT_STATE_SCANNING) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "停止扫描");
    return bt_client_stop_scan();
}

/**
 * @brief 连接到底座设备
 */
esp_err_t bluetooth_system_connect(const esp_bd_addr_t device_addr)
{
    if (!g_bt_system_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (g_bt_state == BT_STATE_CONNECTED) {
        ESP_LOGW(TAG, "已连接到设备");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "连接到底座设备");
    return bt_client_connect(device_addr);
}

/**
 * @brief 断开连接
 */
esp_err_t bluetooth_system_disconnect(void)
{
    if (g_bt_state != BT_STATE_CONNECTED) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "断开连接");
    return bt_client_disconnect();
}

/**
 * @brief 发送数据到底座
 */
esp_err_t bluetooth_system_send_data(bt_data_type_t type, uint8_t cmd,
                                    const uint8_t *data, uint16_t length)
{
    if (g_bt_state != BT_STATE_CONNECTED) {
        ESP_LOGE(TAG, "设备未连接");
        return ESP_ERR_INVALID_STATE;
    }

    if (!bt_client_is_ready()) {
        ESP_LOGE(TAG, "客户端未准备就绪");
        return ESP_ERR_INVALID_STATE;
    }

    // 创建数据包
    uint8_t packet[BT_MAX_DATA_LENGTH];
    uint16_t packet_len = bt_protocol_create_packet(type, cmd, data, length,
                                                   packet, sizeof(packet));
    if (packet_len == 0) {
        ESP_LOGE(TAG, "创建数据包失败");
        return ESP_ERR_INVALID_ARG;
    }

    // 获取服务信息
    const bt_service_discovery_t *service_info = bt_client_get_service_info();
    if (!service_info || !service_info->char_cmd_found) {
        ESP_LOGE(TAG, "服务未发现");
        return ESP_ERR_INVALID_STATE;
    }

    // 发送数据
    esp_err_t ret = bt_client_write_char(service_info->char_cmd_handle, packet, packet_len);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "发送数据成功: type=0x%02X, cmd=0x%02X, len=%d", type, cmd, length);
    } else {
        ESP_LOGE(TAG, "发送数据失败: %s", esp_err_to_name(ret));
    }

    return ret;
}

/**
 * @brief 发送命令到底座
 */
esp_err_t bluetooth_system_send_command(uint8_t cmd, const uint8_t *data, uint16_t length)
{
    return bluetooth_system_send_data(BT_DATA_TYPE_CMD, cmd, data, length);
}

/**
 * @brief 获取连接状态
 */
bt_connection_state_t bluetooth_system_get_state(void)
{
    return g_bt_state;
}

/**
 * @brief 获取连接信息
 */
const bt_connection_info_t* bluetooth_system_get_connection_info(void)
{
    return &g_connection_info;
}

/**
 * @brief 检查是否已连接
 */
bool bluetooth_system_is_connected(void)
{
    return (g_bt_state == BT_STATE_CONNECTED) && bt_client_is_ready();
}

/**
 * @brief 注册事件回调函数
 */
esp_err_t bluetooth_system_register_event_callback(bt_event_callback_t callback)
{
    g_event_callback = callback;
    return ESP_OK;
}

/**
 * @brief 注册数据接收回调函数
 */
esp_err_t bluetooth_system_register_data_callback(bt_data_callback_t callback)
{
    g_data_callback = callback;
    return ESP_OK;
}

/**
 * @brief 获取扫描到的设备列表
 */
uint8_t bluetooth_system_get_scan_results(bt_device_info_t *devices, uint8_t max_count)
{
    if (!devices || max_count == 0) {
        return 0;
    }

    xSemaphoreTake(g_bt_mutex, portMAX_DELAY);

    uint8_t count = (g_scan_count < max_count) ? g_scan_count : max_count;
    memcpy(devices, g_scan_results, count * sizeof(bt_device_info_t));

    xSemaphoreGive(g_bt_mutex);

    return count;
}
