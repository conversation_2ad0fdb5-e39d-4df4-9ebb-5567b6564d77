/**
 * @file time_manager.c
 * @brief TIMO时间管理器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "time_manager.h"
#include "ntp_client.h"
#include "timezone_manager.h"
#include "system_manager.h"
#include "event_manager.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_sntp.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include <sys/time.h>

static const char *TAG = "TIME_MANAGER";

/* 时间管理器状态 */
static bool g_time_manager_initialized = false;
static bool g_time_manager_running = false;
static time_info_t g_time_info;
static time_config_t g_time_config;
static time_event_callback_t g_time_callback = NULL;

/* 任务和定时器 */
static TaskHandle_t g_time_task_handle = NULL;
static esp_timer_handle_t g_sync_timer = NULL;
static SemaphoreHandle_t g_time_mutex = NULL;

/* 默认配置 */
static const time_config_t DEFAULT_TIME_CONFIG = {
    .timezone = "Asia/Shanghai",
    .auto_sync = true,
    .sync_interval_s = 3600,  // 1小时
    .dst_enabled = false,
    .ntp_server = "pool.ntp.org"
};

/**
 * @brief 更新时间信息
 */
static void update_time_info(void)
{
    xSemaphoreTake(g_time_mutex, portMAX_DELAY);
    
    // 获取系统时间
    time(&g_time_info.timestamp);
    localtime_r(&g_time_info.timestamp, &g_time_info.timeinfo);
    
    // 计算时间漂移
    static time_t last_timestamp = 0;
    if (last_timestamp > 0) {
        int64_t expected_time = last_timestamp + 1;
        g_time_info.drift_ms = (g_time_info.timestamp - expected_time) * 1000;
    }
    last_timestamp = g_time_info.timestamp;
    
    xSemaphoreGive(g_time_mutex);
}

/**
 * @brief 时间管理任务
 */
static void time_manager_task(void *pvParameters)
{
    ESP_LOGI(TAG, "时间管理任务启动");
    
    while (g_time_manager_running) {
        // 更新时间信息
        update_time_info();
        
        // 检查时间状态
        if (g_time_info.status == TIME_STATUS_UNSET) {
            // 尝试从RTC读取时间
            rtc_time_t rtc_time;
            if (rtc_get_time(&rtc_time) == ESP_OK) {
                struct tm tm = {
                    .tm_year = rtc_time.year + 100,  // RTC年份是从2000开始
                    .tm_mon = rtc_time.month - 1,
                    .tm_mday = rtc_time.day,
                    .tm_hour = rtc_time.hour,
                    .tm_min = rtc_time.minute,
                    .tm_sec = rtc_time.second,
                    .tm_wday = rtc_time.weekday
                };
                
                time_t timestamp = mktime(&tm);
                if (timestamp > 0) {
                    struct timeval tv = { .tv_sec = timestamp, .tv_usec = 0 };
                    settimeofday(&tv, NULL);
                    g_time_info.source = TIME_SOURCE_RTC;
                    g_time_info.status = TIME_STATUS_RTC_ONLY;
                    ESP_LOGI(TAG, "从RTC恢复时间: %04d-%02d-%02d %02d:%02d:%02d",
                             tm.tm_year + 1900, tm.tm_mon + 1, tm.tm_mday,
                             tm.tm_hour, tm.tm_min, tm.tm_sec);
                }
            }
        }
        
        // 调用回调函数
        if (g_time_callback) {
            g_time_callback(&g_time_info);
        }
        
        // 延时1秒
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
    
    ESP_LOGI(TAG, "时间管理任务结束");
    vTaskDelete(NULL);
}

/**
 * @brief 同步定时器回调
 */
static void sync_timer_callback(void *arg)
{
    if (g_time_config.auto_sync) {
        ESP_LOGI(TAG, "自动同步网络时间");
        time_manager_sync_ntp();
    }
}

/**
 * @brief 初始化时间管理器
 */
esp_err_t time_manager_init(void)
{
    if (g_time_manager_initialized) {
        ESP_LOGW(TAG, "时间管理器已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化时间管理器...");
    
    // 创建互斥锁
    g_time_mutex = xSemaphoreCreateMutex();
    if (!g_time_mutex) {
        ESP_LOGE(TAG, "创建时间互斥锁失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 初始化时间信息
    memset(&g_time_info, 0, sizeof(time_info_t));
    g_time_info.status = TIME_STATUS_UNSET;
    g_time_info.source = TIME_SOURCE_NONE;
    
    // 加载配置
    memcpy(&g_time_config, &DEFAULT_TIME_CONFIG, sizeof(time_config_t));
    
    // 初始化时区管理器
    esp_err_t ret = timezone_manager_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "时区管理器初始化失败");
        return ret;
    }
    
    // 设置时区
    ret = timezone_manager_set_timezone(g_time_config.timezone);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "设置时区失败，使用默认时区");
    }
    
    // 初始化NTP客户端
    ret = ntp_client_init();
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "NTP客户端初始化失败");
    }
    
    // 创建同步定时器
    esp_timer_create_args_t timer_args = {
        .callback = sync_timer_callback,
        .arg = NULL,
        .name = "time_sync_timer"
    };
    
    ret = esp_timer_create(&timer_args, &g_sync_timer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建同步定时器失败");
        return ret;
    }
    
    g_time_manager_initialized = true;
    ESP_LOGI(TAG, "时间管理器初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 启动时间管理器
 */
esp_err_t time_manager_start(void)
{
    if (!g_time_manager_initialized) {
        ESP_LOGE(TAG, "时间管理器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (g_time_manager_running) {
        ESP_LOGW(TAG, "时间管理器已启动");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "启动时间管理器...");
    
    // 创建时间管理任务
    BaseType_t ret = xTaskCreate(
        time_manager_task,
        "time_manager",
        4096,
        NULL,
        5,
        &g_time_task_handle
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建时间管理任务失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 启动同步定时器
    if (g_time_config.auto_sync) {
        esp_err_t timer_ret = esp_timer_start_periodic(g_sync_timer, 
                                                      g_time_config.sync_interval_s * 1000000);
        if (timer_ret != ESP_OK) {
            ESP_LOGW(TAG, "启动同步定时器失败");
        }
    }
    
    g_time_manager_running = true;
    ESP_LOGI(TAG, "时间管理器启动完成");
    
    return ESP_OK;
}

/**
 * @brief 停止时间管理器
 */
esp_err_t time_manager_stop(void)
{
    if (!g_time_manager_running) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "停止时间管理器...");
    
    g_time_manager_running = false;
    
    // 停止同步定时器
    if (g_sync_timer) {
        esp_timer_stop(g_sync_timer);
    }
    
    // 停止时间管理任务
    if (g_time_task_handle) {
        vTaskDelete(g_time_task_handle);
        g_time_task_handle = NULL;
    }
    
    ESP_LOGI(TAG, "时间管理器停止完成");
    return ESP_OK;
}

/**
 * @brief 获取当前时间信息
 */
time_info_t* time_manager_get_time_info(void)
{
    return &g_time_info;
}

/**
 * @brief 获取当前时间戳
 */
time_t time_manager_get_timestamp(void)
{
    time_t now;
    time(&now);
    return now;
}

/**
 * @brief 获取当前时间结构
 */
esp_err_t time_manager_get_localtime(struct tm *tm)
{
    if (!tm) {
        return ESP_ERR_INVALID_ARG;
    }
    
    time_t now;
    time(&now);
    localtime_r(&now, tm);
    
    return ESP_OK;
}

/**
 * @brief 手动设置时间
 */
esp_err_t time_manager_set_time(time_t timestamp)
{
    struct timeval tv = { .tv_sec = timestamp, .tv_usec = 0 };
    int ret = settimeofday(&tv, NULL);
    
    if (ret == 0) {
        g_time_info.source = TIME_SOURCE_MANUAL;
        g_time_info.status = TIME_STATUS_SYNCED;
        g_time_info.last_sync_time = esp_timer_get_time();
        
        // 同步到RTC
        struct tm tm;
        localtime_r(&timestamp, &tm);
        rtc_time_t rtc_time = {
            .year = tm.tm_year - 100,  // RTC年份从2000开始
            .month = tm.tm_mon + 1,
            .day = tm.tm_mday,
            .hour = tm.tm_hour,
            .minute = tm.tm_min,
            .second = tm.tm_sec,
            .weekday = tm.tm_wday
        };
        rtc_set_time(&rtc_time);
        
        ESP_LOGI(TAG, "手动设置时间成功: %04d-%02d-%02d %02d:%02d:%02d",
                 tm.tm_year + 1900, tm.tm_mon + 1, tm.tm_mday,
                 tm.tm_hour, tm.tm_min, tm.tm_sec);
        
        return ESP_OK;
    } else {
        ESP_LOGE(TAG, "设置系统时间失败");
        return ESP_FAIL;
    }
}

/**
 * @brief 手动设置时间(结构体)
 */
esp_err_t time_manager_set_time_struct(struct tm *tm)
{
    if (!tm) {
        return ESP_ERR_INVALID_ARG;
    }
    
    time_t timestamp = mktime(tm);
    return time_manager_set_time(timestamp);
}

/**
 * @brief 同步网络时间
 */
esp_err_t time_manager_sync_ntp(void)
{
    ESP_LOGI(TAG, "开始同步网络时间...");
    
    esp_err_t ret = ntp_client_sync(g_time_config.ntp_server);
    if (ret == ESP_OK) {
        g_time_info.source = TIME_SOURCE_NTP;
        g_time_info.status = TIME_STATUS_SYNCED;
        g_time_info.last_sync_time = esp_timer_get_time();
        
        // 同步到RTC
        time_t now;
        time(&now);
        struct tm tm;
        localtime_r(&now, &tm);
        
        rtc_time_t rtc_time = {
            .year = tm.tm_year - 100,
            .month = tm.tm_mon + 1,
            .day = tm.tm_mday,
            .hour = tm.tm_hour,
            .minute = tm.tm_min,
            .second = tm.tm_sec,
            .weekday = tm.tm_wday
        };
        rtc_set_time(&rtc_time);
        
        ESP_LOGI(TAG, "网络时间同步成功");
        return ESP_OK;
    } else {
        ESP_LOGE(TAG, "网络时间同步失败");
        return ret;
    }
}

/**
 * @brief 同步RTC时间
 */
esp_err_t time_manager_sync_rtc(void)
{
    ESP_LOGI(TAG, "从RTC同步时间...");
    
    rtc_time_t rtc_time;
    esp_err_t ret = rtc_get_time(&rtc_time);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "读取RTC时间失败");
        return ret;
    }
    
    struct tm tm = {
        .tm_year = rtc_time.year + 100,
        .tm_mon = rtc_time.month - 1,
        .tm_mday = rtc_time.day,
        .tm_hour = rtc_time.hour,
        .tm_min = rtc_time.minute,
        .tm_sec = rtc_time.second,
        .tm_wday = rtc_time.weekday
    };
    
    time_t timestamp = mktime(&tm);
    struct timeval tv = { .tv_sec = timestamp, .tv_usec = 0 };
    settimeofday(&tv, NULL);
    
    g_time_info.source = TIME_SOURCE_RTC;
    g_time_info.status = TIME_STATUS_RTC_ONLY;
    
    ESP_LOGI(TAG, "RTC时间同步成功");
    return ESP_OK;
}

/**
 * @brief 设置时区
 */
esp_err_t time_manager_set_timezone(const char *timezone)
{
    if (!timezone) {
        return ESP_ERR_INVALID_ARG;
    }
    
    esp_err_t ret = timezone_manager_set_timezone(timezone);
    if (ret == ESP_OK) {
        strncpy(g_time_config.timezone, timezone, sizeof(g_time_config.timezone) - 1);
        g_time_config.timezone[sizeof(g_time_config.timezone) - 1] = '\0';
        ESP_LOGI(TAG, "设置时区: %s", timezone);
    }
    
    return ret;
}

/**
 * @brief 获取时区
 */
const char* time_manager_get_timezone(void)
{
    return g_time_config.timezone;
}

/**
 * @brief 设置时间配置
 */
esp_err_t time_manager_set_config(const time_config_t *config)
{
    if (!config) {
        return ESP_ERR_INVALID_ARG;
    }
    
    memcpy(&g_time_config, config, sizeof(time_config_t));
    
    // 重新设置时区
    timezone_manager_set_timezone(g_time_config.timezone);
    
    // 重新启动同步定时器
    if (g_sync_timer) {
        esp_timer_stop(g_sync_timer);
        if (g_time_config.auto_sync) {
            esp_timer_start_periodic(g_sync_timer, g_time_config.sync_interval_s * 1000000);
        }
    }
    
    ESP_LOGI(TAG, "时间配置已更新");
    return ESP_OK;
}

/**
 * @brief 获取时间配置
 */
time_config_t* time_manager_get_config(void)
{
    return &g_time_config;
}

/**
 * @brief 注册时间事件回调
 */
esp_err_t time_manager_register_callback(time_event_callback_t callback)
{
    g_time_callback = callback;
    return ESP_OK;
}

/**
 * @brief 格式化时间字符串
 */
esp_err_t time_manager_format_time(const char *format, char *buffer, size_t size)
{
    if (!format || !buffer || size == 0) {
        return ESP_ERR_INVALID_ARG;
    }
    
    time_t now;
    time(&now);
    struct tm tm;
    localtime_r(&now, &tm);
    
    size_t len = strftime(buffer, size, format, &tm);
    return (len > 0) ? ESP_OK : ESP_FAIL;
}

/**
 * @brief 获取星期几
 */
int time_manager_get_weekday(void)
{
    struct tm tm;
    time_manager_get_localtime(&tm);
    return tm.tm_wday;
}

/**
 * @brief 获取年份
 */
int time_manager_get_year(void)
{
    struct tm tm;
    time_manager_get_localtime(&tm);
    return tm.tm_year + 1900;
}

/**
 * @brief 获取月份
 */
int time_manager_get_month(void)
{
    struct tm tm;
    time_manager_get_localtime(&tm);
    return tm.tm_mon + 1;
}

/**
 * @brief 获取日期
 */
int time_manager_get_day(void)
{
    struct tm tm;
    time_manager_get_localtime(&tm);
    return tm.tm_mday;
}

/**
 * @brief 获取小时
 */
int time_manager_get_hour(void)
{
    struct tm tm;
    time_manager_get_localtime(&tm);
    return tm.tm_hour;
}

/**
 * @brief 获取分钟
 */
int time_manager_get_minute(void)
{
    struct tm tm;
    time_manager_get_localtime(&tm);
    return tm.tm_min;
}

/**
 * @brief 获取秒
 */
int time_manager_get_second(void)
{
    struct tm tm;
    time_manager_get_localtime(&tm);
    return tm.tm_sec;
}

/**
 * @brief 检查是否为闰年
 */
bool time_manager_is_leap_year(int year)
{
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
}

/**
 * @brief 获取月份天数
 */
int time_manager_get_days_in_month(int year, int month)
{
    static const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    
    if (month < 1 || month > 12) {
        return 0;
    }
    
    int days = days_in_month[month - 1];
    if (month == 2 && time_manager_is_leap_year(year)) {
        days = 29;
    }
    
    return days;
}
