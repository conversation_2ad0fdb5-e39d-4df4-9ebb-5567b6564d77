/**
 * @file alarm_manager.h
 * @brief TIMO闹钟管理器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef ALARM_MANAGER_H
#define ALARM_MANAGER_H

#include "esp_err.h"
#include <time.h>
#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_ALARMS 10

/* 闹钟重复模式 */
typedef enum {
    ALARM_REPEAT_NONE = 0,      // 不重复
    ALARM_REPEAT_DAILY,         // 每天
    ALARM_REPEAT_WEEKDAYS,      // 工作日
    ALARM_REPEAT_WEEKENDS,      // 周末
    ALARM_REPEAT_WEEKLY,        // 每周
    ALARM_REPEAT_CUSTOM,        // 自定义
    ALARM_REPEAT_MAX
} alarm_repeat_t;

/* 闹钟状态 */
typedef enum {
    ALARM_STATUS_DISABLED = 0,  // 禁用
    ALARM_STATUS_ENABLED,       // 启用
    ALARM_STATUS_TRIGGERED,     // 已触发
    ALARM_STATUS_SNOOZED,       // 贪睡中
    ALARM_STATUS_MAX
} alarm_status_t;

/* 闹钟类型 */
typedef enum {
    ALARM_TYPE_NORMAL = 0,      // 普通闹钟
    ALARM_TYPE_NAP,             // 小憩闹钟
    ALARM_TYPE_REMINDER,        // 提醒
    ALARM_TYPE_TIMER,           // 定时器
    ALARM_TYPE_MAX
} alarm_type_t;

/* 闹钟结构体 */
typedef struct {
    uint8_t id;                 // 闹钟ID
    char name[32];              // 闹钟名称
    alarm_type_t type;          // 闹钟类型
    alarm_status_t status;      // 闹钟状态
    alarm_repeat_t repeat;      // 重复模式
    uint8_t hour;               // 小时 (0-23)
    uint8_t minute;             // 分钟 (0-59)
    uint8_t weekdays;           // 星期掩码 (bit0=周日, bit1=周一, ...)
    uint8_t volume;             // 音量 (0-100)
    uint8_t snooze_duration;    // 贪睡时长(分钟)
    uint8_t snooze_count;       // 贪睡次数
    uint8_t max_snooze;         // 最大贪睡次数
    char ringtone[64];          // 铃声文件路径
    bool vibrate;               // 震动开关
    bool gradual_volume;        // 渐强音量
    time_t created_time;        // 创建时间
    time_t last_triggered;      // 上次触发时间
    time_t next_trigger;        // 下次触发时间
} alarm_t;

/* 闹钟事件类型 */
typedef enum {
    ALARM_EVENT_TRIGGERED = 0,  // 闹钟触发
    ALARM_EVENT_SNOOZED,        // 贪睡
    ALARM_EVENT_DISMISSED,      // 关闭
    ALARM_EVENT_TIMEOUT,        // 超时
    ALARM_EVENT_MAX
} alarm_event_t;

/* 闹钟事件回调函数类型 */
typedef void (*alarm_event_callback_t)(alarm_event_t event, alarm_t *alarm);

/**
 * @brief 初始化闹钟管理器
 * @return esp_err_t 
 */
esp_err_t alarm_manager_init(void);

/**
 * @brief 启动闹钟管理器
 * @return esp_err_t 
 */
esp_err_t alarm_manager_start(void);

/**
 * @brief 停止闹钟管理器
 * @return esp_err_t 
 */
esp_err_t alarm_manager_stop(void);

/**
 * @brief 创建闹钟
 * @param alarm 闹钟结构体
 * @return esp_err_t 
 */
esp_err_t alarm_manager_create(alarm_t *alarm);

/**
 * @brief 删除闹钟
 * @param alarm_id 闹钟ID
 * @return esp_err_t 
 */
esp_err_t alarm_manager_delete(uint8_t alarm_id);

/**
 * @brief 更新闹钟
 * @param alarm 闹钟结构体
 * @return esp_err_t 
 */
esp_err_t alarm_manager_update(const alarm_t *alarm);

/**
 * @brief 启用闹钟
 * @param alarm_id 闹钟ID
 * @return esp_err_t 
 */
esp_err_t alarm_manager_enable(uint8_t alarm_id);

/**
 * @brief 禁用闹钟
 * @param alarm_id 闹钟ID
 * @return esp_err_t 
 */
esp_err_t alarm_manager_disable(uint8_t alarm_id);

/**
 * @brief 获取闹钟
 * @param alarm_id 闹钟ID
 * @return alarm_t* 闹钟指针，NULL表示未找到
 */
alarm_t* alarm_manager_get(uint8_t alarm_id);

/**
 * @brief 获取所有闹钟
 * @param alarms 闹钟数组
 * @param count 数组大小，返回实际数量
 * @return esp_err_t 
 */
esp_err_t alarm_manager_get_all(alarm_t *alarms, uint8_t *count);

/**
 * @brief 获取启用的闹钟数量
 * @return uint8_t 
 */
uint8_t alarm_manager_get_enabled_count(void);

/**
 * @brief 获取下一个闹钟
 * @return alarm_t* 下一个要触发的闹钟，NULL表示无闹钟
 */
alarm_t* alarm_manager_get_next(void);

/**
 * @brief 贪睡闹钟
 * @param alarm_id 闹钟ID
 * @return esp_err_t 
 */
esp_err_t alarm_manager_snooze(uint8_t alarm_id);

/**
 * @brief 关闭闹钟
 * @param alarm_id 闹钟ID
 * @return esp_err_t 
 */
esp_err_t alarm_manager_dismiss(uint8_t alarm_id);

/**
 * @brief 测试闹钟
 * @param alarm_id 闹钟ID
 * @return esp_err_t 
 */
esp_err_t alarm_manager_test(uint8_t alarm_id);

/**
 * @brief 注册闹钟事件回调
 * @param callback 回调函数
 * @return esp_err_t 
 */
esp_err_t alarm_manager_register_callback(alarm_event_callback_t callback);

/**
 * @brief 保存闹钟配置
 * @return esp_err_t 
 */
esp_err_t alarm_manager_save_config(void);

/**
 * @brief 加载闹钟配置
 * @return esp_err_t 
 */
esp_err_t alarm_manager_load_config(void);

/**
 * @brief 计算下次触发时间
 * @param alarm 闹钟结构体
 * @return time_t 下次触发时间
 */
time_t alarm_manager_calculate_next_trigger(const alarm_t *alarm);

/**
 * @brief 检查闹钟是否应该在指定时间触发
 * @param alarm 闹钟结构体
 * @param timestamp 时间戳
 * @return bool true=应该触发, false=不触发
 */
bool alarm_manager_should_trigger(const alarm_t *alarm, time_t timestamp);

/**
 * @brief 格式化闹钟时间字符串
 * @param alarm 闹钟结构体
 * @param buffer 输出缓冲区
 * @param size 缓冲区大小
 * @return esp_err_t 
 */
esp_err_t alarm_manager_format_time(const alarm_t *alarm, char *buffer, size_t size);

/**
 * @brief 格式化重复模式字符串
 * @param repeat 重复模式
 * @param weekdays 星期掩码
 * @param buffer 输出缓冲区
 * @param size 缓冲区大小
 * @return esp_err_t 
 */
esp_err_t alarm_manager_format_repeat(alarm_repeat_t repeat, uint8_t weekdays, char *buffer, size_t size);

#ifdef __cplusplus
}
#endif

#endif /* ALARM_MANAGER_H */
