/**
 * @file timezone_manager.c
 * @brief TIMO时区管理器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "timezone_manager.h"
#include "esp_log.h"
#include <string.h>

static const char *TAG = "TIMEZONE_MANAGER";

static bool g_timezone_initialized = false;
static char g_current_timezone[32] = "Asia/Shanghai";

/**
 * @brief 初始化时区管理器
 */
esp_err_t timezone_manager_init(void)
{
    if (g_timezone_initialized) {
        ESP_LOGW(TAG, "时区管理器已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化时区管理器...");
    
    // 设置默认时区
    setenv("TZ", g_current_timezone, 1);
    tzset();
    
    g_timezone_initialized = true;
    ESP_LOGI(TAG, "时区管理器初始化完成，当前时区: %s", g_current_timezone);
    
    return ESP_OK;
}

/**
 * @brief 设置时区
 */
esp_err_t timezone_manager_set_timezone(const char *timezone)
{
    if (!timezone) {
        return ESP_ERR_INVALID_ARG;
    }
    
    ESP_LOGI(TAG, "设置时区: %s", timezone);
    
    // 更新环境变量
    setenv("TZ", timezone, 1);
    tzset();
    
    // 保存当前时区
    strncpy(g_current_timezone, timezone, sizeof(g_current_timezone) - 1);
    g_current_timezone[sizeof(g_current_timezone) - 1] = '\0';
    
    ESP_LOGI(TAG, "时区设置成功: %s", timezone);
    return ESP_OK;
}

/**
 * @brief 获取当前时区
 */
const char* timezone_manager_get_timezone(void)
{
    return g_current_timezone;
}
