/**
 * @file ambient_effects.c
 * @brief 氛围灯效控制模块实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "base_config.h"
#include "base_main.h"
#include "esp_log.h"
#include "esp_err.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <math.h>
#include <string.h>

static const char *TAG = "AMBIENT";

/* 外部函数声明 */
extern esp_err_t ws2812_init(void);
extern esp_err_t ws2812_start(void);
extern esp_err_t ws2812_set_pixel(uint16_t index, rgb_color_t color);
extern esp_err_t ws2812_set_all_pixels(rgb_color_t color);
extern esp_err_t ws2812_clear_all(void);
extern esp_err_t ws2812_refresh(void);
extern esp_err_t ws2812_set_brightness(uint8_t brightness);
extern esp_err_t ws2812_rainbow_effect(uint8_t brightness, uint16_t speed);
extern esp_err_t ws2812_breathing_effect(rgb_color_t color, uint16_t speed);
extern esp_err_t ws2812_wave_effect(rgb_color_t color, uint8_t brightness, uint16_t speed);

/* 氛围灯效状态 */
static bool g_ambient_initialized = false;
static bool g_ambient_running = false;
static ambient_scene_t g_current_scene = AMBIENT_SCENE_OFF;
static led_config_t g_led_config = {0};

/* 动画参数 */
static uint32_t g_animation_step = 0;
static uint64_t g_last_update_time = 0;

/* 预定义场景配置 */
static const led_config_t SCENE_CONFIGS[AMBIENT_SCENE_MAX] = {
    // AMBIENT_SCENE_OFF
    {.mode = LED_MODE_OFF, .color = COLOR_OFF, .brightness = 0, .speed = 0, .enabled = false},
    
    // AMBIENT_SCENE_STANDBY
    {.mode = LED_MODE_BREATHING, .color = COLOR_COOL_WHITE, .brightness = 30, .speed = 2, .enabled = true},
    
    // AMBIENT_SCENE_CONVERSATION
    {.mode = LED_MODE_WAVE, .color = COLOR_BLUE, .brightness = 80, .speed = 5, .enabled = true},
    
    // AMBIENT_SCENE_MORNING_WAKE
    {.mode = LED_MODE_BREATHING, .color = COLOR_WARM_WHITE, .brightness = 100, .speed = 3, .enabled = true},
    
    // AMBIENT_SCENE_NAP_WAKE
    {.mode = LED_MODE_BREATHING, .color = COLOR_ORANGE, .brightness = 60, .speed = 2, .enabled = true},
    
    // AMBIENT_SCENE_SLEEP_AID
    {.mode = LED_MODE_BREATHING, .color = COLOR_PURPLE, .brightness = 20, .speed = 1, .enabled = true},
    
    // AMBIENT_SCENE_TODO_REMIND
    {.mode = LED_MODE_FLASH, .color = COLOR_YELLOW, .brightness = 70, .speed = 3, .enabled = true},
    
    // AMBIENT_SCENE_ALERT_LOW
    {.mode = LED_MODE_BREATHING, .color = COLOR_YELLOW, .brightness = 50, .speed = 4, .enabled = true},
    
    // AMBIENT_SCENE_ALERT_MEDIUM
    {.mode = LED_MODE_FLASH, .color = COLOR_ORANGE, .brightness = 80, .speed = 6, .enabled = true},
    
    // AMBIENT_SCENE_ALERT_HIGH
    {.mode = LED_MODE_FLASH, .color = COLOR_RED, .brightness = 100, .speed = 10, .enabled = true},
    
    // AMBIENT_SCENE_FOCUS_MODE
    {.mode = LED_MODE_SOLID, .color = COLOR_GREEN, .brightness = 40, .speed = 0, .enabled = true},
    
    // AMBIENT_SCENE_CHARGING
    {.mode = LED_MODE_WAVE, .color = COLOR_GREEN, .brightness = 60, .speed = 3, .enabled = true},
    
    // AMBIENT_SCENE_PAIRING
    {.mode = LED_MODE_RAINBOW, .color = COLOR_WHITE, .brightness = 80, .speed = 8, .enabled = true}
};

/**
 * @brief 氛围灯效更新任务
 */
static void ambient_update_task(void *pvParameters)
{
    ESP_LOGI(TAG, "氛围灯效更新任务启动");
    
    while (g_ambient_running) {
        uint64_t current_time = esp_timer_get_time() / 1000;
        
        // 检查是否需要更新
        if (current_time - g_last_update_time >= AMBIENT_UPDATE_RATE_MS) {
            g_last_update_time = current_time;
            
            // 根据当前场景更新灯效
            switch (g_led_config.mode) {
                case LED_MODE_OFF:
                    ws2812_clear_all();
                    ws2812_refresh();
                    break;
                    
                case LED_MODE_SOLID:
                    ws2812_set_all_pixels(g_led_config.color);
                    ws2812_refresh();
                    break;
                    
                case LED_MODE_BREATHING:
                    ws2812_breathing_effect(g_led_config.color, g_led_config.speed);
                    break;
                    
                case LED_MODE_RAINBOW:
                    ws2812_rainbow_effect(g_led_config.brightness, g_led_config.speed);
                    break;
                    
                case LED_MODE_WAVE:
                    ws2812_wave_effect(g_led_config.color, g_led_config.brightness, g_led_config.speed);
                    break;
                    
                case LED_MODE_FLASH:
                    ambient_flash_effect();
                    break;
                    
                case LED_MODE_MUSIC_SYNC:
                    ambient_music_sync_effect();
                    break;
                    
                case LED_MODE_NOTIFICATION:
                    ambient_notification_effect();
                    break;
                    
                default:
                    break;
            }
            
            g_animation_step++;
        }
        
        vTaskDelay(pdMS_TO_TICKS(10));
    }
    
    vTaskDelete(NULL);
}

/**
 * @brief 初始化氛围灯效控制器
 */
esp_err_t ambient_effects_init(void)
{
    if (g_ambient_initialized) {
        ESP_LOGW(TAG, "氛围灯效控制器已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化氛围灯效控制器...");
    
    // 初始化WS2812驱动
    esp_err_t ret = ws2812_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "WS2812驱动初始化失败");
        return ret;
    }
    
    // 设置默认配置
    g_led_config = SCENE_CONFIGS[AMBIENT_SCENE_STANDBY];
    g_current_scene = AMBIENT_SCENE_STANDBY;
    
    g_ambient_initialized = true;
    ESP_LOGI(TAG, "氛围灯效控制器初始化完成");
    return ESP_OK;
}

/**
 * @brief 启动氛围灯效控制器
 */
esp_err_t ambient_effects_start(void)
{
    if (!g_ambient_initialized) {
        ESP_LOGE(TAG, "氛围灯效控制器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    // 启动WS2812驱动
    esp_err_t ret = ws2812_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "WS2812驱动启动失败");
        return ret;
    }
    
    g_ambient_running = true;
    
    // 创建更新任务
    BaseType_t task_ret = xTaskCreate(
        ambient_update_task,
        "ambient_update",
        TASK_STACK_SIZE_MEDIUM,
        NULL,
        TASK_PRIORITY_NORMAL,
        NULL
    );
    
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "创建氛围灯效更新任务失败");
        g_ambient_running = false;
        return ESP_ERR_NO_MEM;
    }
    
    ESP_LOGI(TAG, "氛围灯效控制器启动");
    return ESP_OK;
}

/**
 * @brief 停止氛围灯效控制器
 */
esp_err_t ambient_effects_stop(void)
{
    g_ambient_running = false;
    
    // 关闭所有LED
    ws2812_clear_all();
    ws2812_refresh();
    
    ESP_LOGI(TAG, "氛围灯效控制器停止");
    return ESP_OK;
}

/**
 * @brief 设置氛围场景
 */
esp_err_t ambient_effects_set_scene(ambient_scene_t scene)
{
    if (!g_ambient_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (scene >= AMBIENT_SCENE_MAX) {
        return ESP_ERR_INVALID_ARG;
    }
    
    ESP_LOGI(TAG, "切换氛围场景: %d", scene);
    
    g_current_scene = scene;
    g_led_config = SCENE_CONFIGS[scene];
    g_animation_step = 0;
    
    // 设置亮度
    ws2812_set_brightness(g_led_config.brightness);
    
    return ESP_OK;
}

/**
 * @brief 获取当前氛围场景
 */
ambient_scene_t ambient_effects_get_scene(void)
{
    return g_current_scene;
}

/**
 * @brief 设置自定义灯效配置
 */
esp_err_t ambient_effects_set_config(const led_config_t *config)
{
    if (!g_ambient_initialized || !config) {
        return ESP_ERR_INVALID_ARG;
    }
    
    g_led_config = *config;
    g_current_scene = AMBIENT_SCENE_MAX; // 表示自定义模式
    g_animation_step = 0;
    
    // 设置亮度
    ws2812_set_brightness(g_led_config.brightness);
    
    ESP_LOGI(TAG, "设置自定义灯效配置");
    return ESP_OK;
}

/**
 * @brief 获取当前灯效配置
 */
led_config_t* ambient_effects_get_config(void)
{
    return &g_led_config;
}

/**
 * @brief 闪烁效果
 */
void ambient_flash_effect(void)
{
    static bool flash_state = false;
    static uint32_t last_flash_time = 0;
    
    uint32_t current_time = g_animation_step * AMBIENT_UPDATE_RATE_MS;
    uint32_t flash_interval = 1000 / g_led_config.speed; // 根据速度计算闪烁间隔
    
    if (current_time - last_flash_time >= flash_interval) {
        flash_state = !flash_state;
        last_flash_time = current_time;
        
        if (flash_state) {
            ws2812_set_all_pixels(g_led_config.color);
        } else {
            rgb_color_t black = COLOR_OFF;
            ws2812_set_all_pixels(black);
        }
        ws2812_refresh();
    }
}

/**
 * @brief 音乐同步效果
 */
void ambient_music_sync_effect(void)
{
    // 这里可以根据声音传感器数据来同步灯效
    extern uint16_t sound_sensor_get_current_level(void);
    
    uint16_t sound_level = sound_sensor_get_current_level();
    
    // 根据声音级别调整亮度
    uint8_t brightness = (sound_level * g_led_config.brightness) / 1000;
    if (brightness < 10) brightness = 10;
    
    // 根据声音级别调整颜色
    rgb_color_t color = g_led_config.color;
    color.r = (color.r * brightness) / 255;
    color.g = (color.g * brightness) / 255;
    color.b = (color.b * brightness) / 255;
    
    ws2812_set_all_pixels(color);
    ws2812_refresh();
}

/**
 * @brief 通知效果
 */
void ambient_notification_effect(void)
{
    // 简单的通知效果：快速闪烁3次
    static uint8_t flash_count = 0;
    static bool flash_state = false;
    static uint32_t last_flash_time = 0;
    
    uint32_t current_time = g_animation_step * AMBIENT_UPDATE_RATE_MS;
    
    if (current_time - last_flash_time >= 200) { // 200ms间隔
        flash_state = !flash_state;
        last_flash_time = current_time;
        
        if (flash_state) {
            ws2812_set_all_pixels(g_led_config.color);
            flash_count++;
        } else {
            rgb_color_t black = COLOR_OFF;
            ws2812_set_all_pixels(black);
        }
        ws2812_refresh();
        
        // 闪烁3次后停止
        if (flash_count >= 3) {
            flash_count = 0;
            g_led_config.mode = LED_MODE_OFF;
        }
    }
}
