/**
 * @file hardware_init.c
 * @brief 硬件初始化主文件
 * @version 1.0.0
 * @date 2025-06-27
 * 
 * 统一管理所有硬件组件的初始化顺序和依赖关系
 */

#include "hardware_hal.h"
#include "hardware_config.h"
#include "i2c_bus.h"
#include "spi_bus.h"
#include "gpio_expander.h"
#include "sensor_drivers.h"
#include "lcd_driver.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

/* 函数声明 */
bool hardware_is_ready(void);
void hardware_print_status(void);

static const char *TAG = "HARDWARE_INIT";

/* 硬件状态 */
static hardware_status_t g_hardware_status = {0};

/**
 * @brief 初始化所有硬件组件
 */
esp_err_t hardware_init_all(void)
{
    ESP_LOGI(TAG, "开始初始化所有硬件组件...");
    
    esp_err_t ret;
    int success_count = 0;
    int total_count = 0;
    
    // 1. 初始化I2C总线 (最高优先级，其他设备依赖)
    ESP_LOGI(TAG, "[1/9] 初始化I2C总线...");
    total_count++;
    ret = i2c_bus_init();
    if (ret == ESP_OK) {
        g_hardware_status.i2c_initialized = true;
        success_count++;
        ESP_LOGI(TAG, "✓ I2C总线初始化成功");
    } else {
        ESP_LOGE(TAG, "✗ I2C总线初始化失败: %s", esp_err_to_name(ret));
    }
    
    // 2. 初始化GPIO扩展芯片 (高优先级，LCD和SD卡依赖)
    ESP_LOGI(TAG, "[2/9] 初始化GPIO扩展芯片...");
    total_count++;
    if (g_hardware_status.i2c_initialized) {
        ret = gpio_expander_init();
        if (ret == ESP_OK) {
            g_hardware_status.exio_initialized = true;
            success_count++;
            ESP_LOGI(TAG, "✓ GPIO扩展芯片初始化成功");
        } else {
            ESP_LOGE(TAG, "✗ GPIO扩展芯片初始化失败: %s", esp_err_to_name(ret));
        }
    } else {
        ESP_LOGW(TAG, "⚠ 跳过GPIO扩展芯片初始化 (I2C总线未就绪)");
    }
    
    // 3. 初始化SPI总线
    ESP_LOGI(TAG, "[3/9] 初始化SPI总线...");
    total_count++;
    ret = hardware_spi_init();
    if (ret == ESP_OK) {
        g_hardware_status.spi_initialized = true;
        success_count++;
        ESP_LOGI(TAG, "✓ SPI总线初始化成功");
    } else {
        ESP_LOGE(TAG, "✗ SPI总线初始化失败: %s", esp_err_to_name(ret));
    }
    
    // 4. 初始化传感器
    ESP_LOGI(TAG, "[4/9] 初始化传感器...");
    total_count++;
    if (g_hardware_status.i2c_initialized) {
        ret = sensors_init();
        if (ret == ESP_OK) {
            g_hardware_status.sensors_initialized = true;
            success_count++;
            ESP_LOGI(TAG, "✓ 传感器初始化成功");
        } else {
            ESP_LOGE(TAG, "✗ 传感器初始化失败: %s", esp_err_to_name(ret));
        }
    } else {
        ESP_LOGW(TAG, "⚠ 跳过传感器初始化 (I2C总线未就绪)");
    }
    
    // 5. 初始化LCD显示屏
    ESP_LOGI(TAG, "[5/9] 初始化LCD显示屏...");
    total_count++;
    if (g_hardware_status.spi_initialized && g_hardware_status.exio_initialized) {
        ret = lcd_init();
        if (ret == ESP_OK) {
            g_hardware_status.lcd_initialized = true;
            success_count++;
            ESP_LOGI(TAG, "✓ LCD显示屏初始化成功");
            
            // 显示启动画面
            lcd_fill_screen(LCD_COLOR_BLACK);
            vTaskDelay(pdMS_TO_TICKS(100));
        } else {
            ESP_LOGE(TAG, "✗ LCD显示屏初始化失败: %s", esp_err_to_name(ret));
        }
    } else {
        ESP_LOGW(TAG, "⚠ 跳过LCD初始化 (SPI或GPIO扩展未就绪)");
    }
    
    // 6. 初始化触摸屏
    ESP_LOGI(TAG, "[6/9] 初始化触摸屏...");
    total_count++;
    if (g_hardware_status.i2c_initialized && g_hardware_status.exio_initialized) {
        ret = touch_init();
        if (ret == ESP_OK) {
            g_hardware_status.touch_initialized = true;
            success_count++;
            ESP_LOGI(TAG, "✓ 触摸屏初始化成功");
        } else {
            ESP_LOGE(TAG, "✗ 触摸屏初始化失败: %s", esp_err_to_name(ret));
        }
    } else {
        ESP_LOGW(TAG, "⚠ 跳过触摸屏初始化 (I2C或GPIO扩展未就绪)");
    }
    
    // 7. 初始化I2S音频接口
    ESP_LOGI(TAG, "[7/9] 初始化I2S音频接口...");
    total_count++;
    ret = hardware_i2s_init();
    if (ret == ESP_OK) {
        g_hardware_status.i2s_initialized = true;
        success_count++;
        ESP_LOGI(TAG, "✓ I2S音频接口初始化成功");
    } else {
        ESP_LOGE(TAG, "✗ I2S音频接口初始化失败: %s", esp_err_to_name(ret));
    }
    
    // 8. 初始化音频系统
    ESP_LOGI(TAG, "[8/9] 初始化音频系统...");
    total_count++;
    if (g_hardware_status.i2c_initialized && g_hardware_status.i2s_initialized) {
        ret = audio_init();
        if (ret == ESP_OK) {
            g_hardware_status.audio_initialized = true;
            success_count++;
            ESP_LOGI(TAG, "✓ 音频系统初始化成功");
        } else {
            ESP_LOGE(TAG, "✗ 音频系统初始化失败: %s", esp_err_to_name(ret));
        }
    } else {
        ESP_LOGW(TAG, "⚠ 跳过音频系统初始化 (I2C或I2S未就绪)");
    }
    
    // 9. 初始化SD卡
    ESP_LOGI(TAG, "[9/9] 初始化SD卡...");
    total_count++;
    if (g_hardware_status.spi_initialized && g_hardware_status.exio_initialized) {
        ret = sd_card_init();
        if (ret == ESP_OK) {
            g_hardware_status.sd_initialized = true;
            success_count++;
            ESP_LOGI(TAG, "✓ SD卡初始化成功");
        } else {
            ESP_LOGW(TAG, "⚠ SD卡初始化失败 (可能未插入): %s", esp_err_to_name(ret));
        }
    } else {
        ESP_LOGW(TAG, "⚠ 跳过SD卡初始化 (SPI或GPIO扩展未就绪)");
    }
    
    // 初始化完成统计
    ESP_LOGI(TAG, "硬件初始化完成: %d/%d 成功", success_count, total_count);
    
    if (success_count >= 6) {  // 至少6个核心组件成功
        ESP_LOGI(TAG, "✓ 系统硬件初始化成功，可以正常运行");
        return ESP_OK;
    } else {
        ESP_LOGE(TAG, "✗ 系统硬件初始化失败，成功组件不足");
        return ESP_ERR_INVALID_STATE;
    }
}

/**
 * @brief 反初始化所有硬件组件
 */
esp_err_t hardware_deinit_all(void)
{
    ESP_LOGI(TAG, "开始反初始化所有硬件组件...");
    
    // 按相反顺序反初始化
    if (g_hardware_status.sd_initialized) {
        sd_card_deinit();
        g_hardware_status.sd_initialized = false;
        ESP_LOGI(TAG, "SD卡反初始化完成");
    }
    
    if (g_hardware_status.audio_initialized) {
        // audio_deinit(); // TODO: 实现音频反初始化
        g_hardware_status.audio_initialized = false;
        ESP_LOGI(TAG, "音频系统反初始化完成");
    }
    
    if (g_hardware_status.touch_initialized) {
        // touch_deinit(); // TODO: 实现触摸屏反初始化
        g_hardware_status.touch_initialized = false;
        ESP_LOGI(TAG, "触摸屏反初始化完成");
    }
    
    if (g_hardware_status.lcd_initialized) {
        lcd_deinit();
        g_hardware_status.lcd_initialized = false;
        ESP_LOGI(TAG, "LCD显示屏反初始化完成");
    }
    
    if (g_hardware_status.exio_initialized) {
        gpio_expander_deinit();
        g_hardware_status.exio_initialized = false;
        ESP_LOGI(TAG, "GPIO扩展芯片反初始化完成");
    }
    
    if (g_hardware_status.i2c_initialized) {
        i2c_bus_deinit();
        g_hardware_status.i2c_initialized = false;
        ESP_LOGI(TAG, "I2C总线反初始化完成");
    }
    
    ESP_LOGI(TAG, "所有硬件组件反初始化完成");
    return ESP_OK;
}

/**
 * @brief 获取硬件状态
 */
hardware_status_t* hardware_get_status(void)
{
    return &g_hardware_status;
}

/**
 * @brief 检查关键硬件是否就绪
 */
bool hardware_is_ready(void)
{
    return (g_hardware_status.i2c_initialized && 
            g_hardware_status.exio_initialized && 
            g_hardware_status.spi_initialized &&
            g_hardware_status.lcd_initialized);
}

/**
 * @brief 打印硬件状态
 */
void hardware_print_status(void)
{
    ESP_LOGI(TAG, "=== 硬件状态 ===");
    ESP_LOGI(TAG, "I2C总线:     %s", g_hardware_status.i2c_initialized ? "✓" : "✗");
    ESP_LOGI(TAG, "SPI总线:     %s", g_hardware_status.spi_initialized ? "✓" : "✗");
    ESP_LOGI(TAG, "I2S音频:     %s", g_hardware_status.i2s_initialized ? "✓" : "✗");
    ESP_LOGI(TAG, "GPIO扩展:    %s", g_hardware_status.exio_initialized ? "✓" : "✗");
    ESP_LOGI(TAG, "LCD显示:     %s", g_hardware_status.lcd_initialized ? "✓" : "✗");
    ESP_LOGI(TAG, "触摸屏:      %s", g_hardware_status.touch_initialized ? "✓" : "✗");
    ESP_LOGI(TAG, "传感器:      %s", g_hardware_status.sensors_initialized ? "✓" : "✗");
    ESP_LOGI(TAG, "音频系统:    %s", g_hardware_status.audio_initialized ? "✓" : "✗");
    ESP_LOGI(TAG, "SD卡:        %s", g_hardware_status.sd_initialized ? "✓" : "✗");
    ESP_LOGI(TAG, "系统就绪:    %s", hardware_is_ready() ? "✓" : "✗");
    ESP_LOGI(TAG, "===============");
}
