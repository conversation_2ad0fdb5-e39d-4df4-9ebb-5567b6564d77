/**
 * @file bt_scene_sync.c
 * @brief TIMO氛围场景同步模块实现
 * @version 1.0.0
 * @date 2025-06-28
 */

#include "bt_scene_sync.h"
#include "bluetooth_system.h"
#include "bt_protocol.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include <string.h>

static const char *TAG = "BT_SCENE_SYNC";

/* 同步状态 */
static bool g_sync_initialized = false;
static bt_sync_state_t g_sync_state = BT_SYNC_STATE_IDLE;
static bt_scene_sync_callback_t g_sync_callback = NULL;

/* 当前场景信息 */
static bt_scene_id_t g_current_scene = BT_SCENE_OFF;
static uint8_t g_current_brightness = 50;
static bool g_music_mode_active = false;
static bt_music_mode_t g_music_mode = BT_MUSIC_MODE_BASIC;

/* 互斥锁 */
static SemaphoreHandle_t g_sync_mutex = NULL;

/**
 * @brief 蓝牙数据接收回调
 */
static void bt_data_callback(const bt_data_packet_t *packet)
{
    if (!packet) {
        return;
    }
    
    ESP_LOGD(TAG, "收到蓝牙数据: cmd=0x%02X", packet->cmd);
    
    switch (packet->cmd) {
        case BT_CMD_LED_SET_SCENE:
            ESP_LOGI(TAG, "场景设置响应");
            g_sync_state = BT_SYNC_STATE_SYNCED;
            if (g_sync_callback) {
                g_sync_callback(g_current_scene, true);
            }
            break;
            
        case BT_CMD_LED_SET_COLOR:
            ESP_LOGI(TAG, "颜色设置响应");
            g_sync_state = BT_SYNC_STATE_SYNCED;
            if (g_sync_callback) {
                g_sync_callback(BT_SCENE_CUSTOM, true);
            }
            break;
            
        case BT_CMD_ERROR:
            ESP_LOGE(TAG, "底座返回错误");
            g_sync_state = BT_SYNC_STATE_ERROR;
            if (g_sync_callback) {
                g_sync_callback(g_current_scene, false);
            }
            break;
            
        case BT_CMD_GET_STATUS:
            if (packet->length >= sizeof(bt_device_status_t)) {
                bt_device_status_t *status = (bt_device_status_t*)packet->data;
                ESP_LOGI(TAG, "底座状态: LED=%d, 场景=%d, 亮度=%d", 
                         status->led_status, status->current_scene, status->brightness);
            }
            break;
            
        default:
            break;
    }
}

/**
 * @brief 初始化场景同步模块
 */
esp_err_t bt_scene_sync_init(void)
{
    if (g_sync_initialized) {
        ESP_LOGW(TAG, "场景同步模块已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化场景同步模块...");
    
    // 创建互斥锁
    g_sync_mutex = xSemaphoreCreateMutex();
    if (!g_sync_mutex) {
        ESP_LOGE(TAG, "创建互斥锁失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 注册蓝牙数据回调
    esp_err_t ret = bluetooth_system_register_data_callback(bt_data_callback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "注册蓝牙数据回调失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 初始化状态
    g_sync_state = BT_SYNC_STATE_IDLE;
    g_current_scene = BT_SCENE_OFF;
    g_current_brightness = 50;
    g_music_mode_active = false;
    
    g_sync_initialized = true;
    ESP_LOGI(TAG, "场景同步模块初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 反初始化场景同步模块
 */
esp_err_t bt_scene_sync_deinit(void)
{
    if (!g_sync_initialized) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "反初始化场景同步模块...");
    
    // 停止音乐模式
    if (g_music_mode_active) {
        bt_scene_sync_stop_music_mode();
    }
    
    // 清理资源
    if (g_sync_mutex) {
        vSemaphoreDelete(g_sync_mutex);
        g_sync_mutex = NULL;
    }
    
    g_sync_initialized = false;
    ESP_LOGI(TAG, "场景同步模块反初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 同步氛围场景到底座
 */
esp_err_t bt_scene_sync_set_scene(bt_scene_id_t scene_id, uint8_t brightness, uint16_t duration_ms)
{
    if (!g_sync_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (!bluetooth_system_is_connected()) {
        ESP_LOGE(TAG, "蓝牙未连接");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "设置场景: %d, 亮度: %d, 持续时间: %d ms", scene_id, brightness, duration_ms);
    
    xSemaphoreTake(g_sync_mutex, portMAX_DELAY);
    
    // 创建场景参数
    bt_scene_params_t params = {
        .scene_id = scene_id,
        .brightness = brightness,
        .duration_ms = duration_ms,
        .color = {0, 0, 0, brightness}
    };
    
    // 发送场景设置命令
    esp_err_t ret = bluetooth_system_send_command(BT_CMD_LED_SET_SCENE, 
                                                 (uint8_t*)&params, sizeof(params));
    if (ret == ESP_OK) {
        g_sync_state = BT_SYNC_STATE_SYNCING;
        g_current_scene = scene_id;
        g_current_brightness = brightness;
    }
    
    xSemaphoreGive(g_sync_mutex);
    
    return ret;
}

/**
 * @brief 设置自定义颜色场景
 */
esp_err_t bt_scene_sync_set_custom_color(uint8_t red, uint8_t green, uint8_t blue, uint8_t brightness)
{
    if (!g_sync_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (!bluetooth_system_is_connected()) {
        ESP_LOGE(TAG, "蓝牙未连接");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "设置自定义颜色: R=%d, G=%d, B=%d, 亮度=%d", red, green, blue, brightness);
    
    xSemaphoreTake(g_sync_mutex, portMAX_DELAY);
    
    // 创建颜色参数
    bt_led_color_t color = {
        .red = red,
        .green = green,
        .blue = blue,
        .brightness = brightness
    };
    
    // 发送颜色设置命令
    esp_err_t ret = bluetooth_system_send_command(BT_CMD_LED_SET_COLOR, 
                                                 (uint8_t*)&color, sizeof(color));
    if (ret == ESP_OK) {
        g_sync_state = BT_SYNC_STATE_SYNCING;
        g_current_scene = BT_SCENE_CUSTOM;
        g_current_brightness = brightness;
    }
    
    xSemaphoreGive(g_sync_mutex);
    
    return ret;
}

/**
 * @brief 开始音乐律动模式
 */
esp_err_t bt_scene_sync_start_music_mode(bt_music_mode_t mode)
{
    if (!g_sync_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (!bluetooth_system_is_connected()) {
        ESP_LOGE(TAG, "蓝牙未连接");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "开始音乐律动模式: %d", mode);
    
    xSemaphoreTake(g_sync_mutex, portMAX_DELAY);
    
    // 发送音乐开始命令
    uint8_t mode_data = mode;
    esp_err_t ret = bluetooth_system_send_command(BT_CMD_MUSIC_START, &mode_data, 1);
    if (ret == ESP_OK) {
        g_music_mode_active = true;
        g_music_mode = mode;
    }
    
    xSemaphoreGive(g_sync_mutex);
    
    return ret;
}

/**
 * @brief 停止音乐律动模式
 */
esp_err_t bt_scene_sync_stop_music_mode(void)
{
    if (!g_sync_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (!bluetooth_system_is_connected()) {
        ESP_LOGE(TAG, "蓝牙未连接");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "停止音乐律动模式");
    
    xSemaphoreTake(g_sync_mutex, portMAX_DELAY);
    
    // 发送音乐停止命令
    esp_err_t ret = bluetooth_system_send_command(BT_CMD_MUSIC_STOP, NULL, 0);
    if (ret == ESP_OK) {
        g_music_mode_active = false;
    }
    
    xSemaphoreGive(g_sync_mutex);
    
    return ret;
}

/**
 * @brief 发送音频数据用于律动
 */
esp_err_t bt_scene_sync_send_audio_data(uint8_t volume, const uint8_t *spectrum_data)
{
    if (!g_sync_initialized || !g_music_mode_active) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (!bluetooth_system_is_connected()) {
        return ESP_ERR_INVALID_STATE;
    }
    
    // 创建音频数据包
    bt_audio_data_t audio_data = {
        .mode = g_music_mode,
        .volume = volume,
        .frequency = 0
    };
    
    if (spectrum_data) {
        memcpy(audio_data.spectrum, spectrum_data, sizeof(audio_data.spectrum));
    }
    
    return bluetooth_system_send_command(BT_CMD_MUSIC_SEND_DATA,
                                        (uint8_t*)&audio_data, sizeof(audio_data));
}

/**
 * @brief 关闭底座LED
 */
esp_err_t bt_scene_sync_turn_off(void)
{
    ESP_LOGI(TAG, "关闭底座LED");
    return bt_scene_sync_set_scene(BT_SCENE_OFF, 0, 0);
}

/**
 * @brief 开启底座LED
 */
esp_err_t bt_scene_sync_turn_on(uint8_t brightness)
{
    ESP_LOGI(TAG, "开启底座LED，亮度: %d", brightness);
    return bt_scene_sync_set_scene(BT_SCENE_STANDBY, brightness, 0);
}

/**
 * @brief 设置底座亮度
 */
esp_err_t bt_scene_sync_set_brightness(uint8_t brightness)
{
    if (!g_sync_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (!bluetooth_system_is_connected()) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "设置亮度: %d", brightness);

    return bluetooth_system_send_command(BT_CMD_LED_SET_BRIGHTNESS, &brightness, 1);
}

/**
 * @brief 获取底座状态
 */
esp_err_t bt_scene_sync_get_status(void)
{
    if (!g_sync_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (!bluetooth_system_is_connected()) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "获取底座状态");

    return bluetooth_system_send_command(BT_CMD_GET_STATUS, NULL, 0);
}

/**
 * @brief 检查是否已同步
 */
bool bt_scene_sync_is_synced(void)
{
    return (g_sync_state == BT_SYNC_STATE_SYNCED) && bluetooth_system_is_connected();
}

/**
 * @brief 获取同步状态
 */
bt_sync_state_t bt_scene_sync_get_state(void)
{
    return g_sync_state;
}

/**
 * @brief 注册场景同步回调函数
 */
esp_err_t bt_scene_sync_register_callback(bt_scene_sync_callback_t callback)
{
    g_sync_callback = callback;
    return ESP_OK;
}

/* 预定义场景快捷函数 */

/**
 * @brief 设置待机模式
 */
esp_err_t bt_scene_sync_standby(uint8_t brightness)
{
    return bt_scene_sync_set_scene(BT_SCENE_STANDBY, brightness, 0);
}

/**
 * @brief 设置对话律动模式
 */
esp_err_t bt_scene_sync_conversation(void)
{
    return bt_scene_sync_set_scene(BT_SCENE_CONVERSATION, 80, 0);
}

/**
 * @brief 设置晨间唤醒模式
 */
esp_err_t bt_scene_sync_morning_wake(void)
{
    return bt_scene_sync_set_scene(BT_SCENE_MORNING_WAKE, 100, 30000); // 30秒
}

/**
 * @brief 设置小憩唤醒模式
 */
esp_err_t bt_scene_sync_nap_wake(void)
{
    return bt_scene_sync_set_scene(BT_SCENE_NAP_WAKE, 70, 15000); // 15秒
}

/**
 * @brief 设置助眠模式
 */
esp_err_t bt_scene_sync_sleep(void)
{
    return bt_scene_sync_set_scene(BT_SCENE_SLEEP, 20, 0);
}

/**
 * @brief 设置待办提醒模式
 */
esp_err_t bt_scene_sync_todo_reminder(void)
{
    return bt_scene_sync_set_scene(BT_SCENE_TODO_REMINDER, 60, 5000); // 5秒
}

/**
 * @brief 设置预警模式
 */
esp_err_t bt_scene_sync_warning(uint8_t level)
{
    bt_scene_id_t scene_id;
    uint8_t brightness;
    uint16_t duration;

    switch (level) {
        case 0: // 低级预警
            scene_id = BT_SCENE_WARNING_LOW;
            brightness = 50;
            duration = 3000;
            break;
        case 1: // 中级预警
            scene_id = BT_SCENE_WARNING_MID;
            brightness = 75;
            duration = 5000;
            break;
        case 2: // 高级预警
            scene_id = BT_SCENE_WARNING_HIGH;
            brightness = 100;
            duration = 10000;
            break;
        default:
            return ESP_ERR_INVALID_ARG;
    }

    return bt_scene_sync_set_scene(scene_id, brightness, duration);
}

/**
 * @brief 设置专注模式
 */
esp_err_t bt_scene_sync_focus(void)
{
    return bt_scene_sync_set_scene(BT_SCENE_FOCUS, 40, 0);
}

/**
 * @brief 设置充电状态模式
 */
esp_err_t bt_scene_sync_charging(void)
{
    return bt_scene_sync_set_scene(BT_SCENE_CHARGING, 30, 0);
}

/**
 * @brief 设置配对模式
 */
esp_err_t bt_scene_sync_pairing(void)
{
    return bt_scene_sync_set_scene(BT_SCENE_PAIRING, 90, 0);
}
