/**
 * @file i2c_bus.h
 * @brief I2C总线驱动头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef I2C_BUS_H
#define I2C_BUS_H

#include "esp_err.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化I2C总线
 * @return esp_err_t 
 */
esp_err_t i2c_bus_init(void);

/**
 * @brief I2C写多个字节
 * @param device_addr 设备地址
 * @param reg_addr 寄存器地址
 * @param data 数据缓冲区
 * @param len 数据长度
 * @return esp_err_t 
 */
esp_err_t i2c_bus_write_bytes(uint8_t device_addr, uint8_t reg_addr, uint8_t *data, size_t len);

/**
 * @brief I2C读多个字节
 * @param device_addr 设备地址
 * @param reg_addr 寄存器地址
 * @param data 数据缓冲区
 * @param len 数据长度
 * @return esp_err_t 
 */
esp_err_t i2c_bus_read_bytes(uint8_t device_addr, uint8_t reg_addr, uint8_t *data, size_t len);

/**
 * @brief I2C写单个字节
 * @param device_addr 设备地址
 * @param reg_addr 寄存器地址
 * @param data 数据
 * @return esp_err_t 
 */
esp_err_t i2c_bus_write_byte(uint8_t device_addr, uint8_t reg_addr, uint8_t data);

/**
 * @brief I2C读单个字节
 * @param device_addr 设备地址
 * @param reg_addr 寄存器地址
 * @param data 数据指针
 * @return esp_err_t 
 */
esp_err_t i2c_bus_read_byte(uint8_t device_addr, uint8_t reg_addr, uint8_t *data);

/**
 * @brief 检查I2C设备是否在线
 * @param device_addr 设备地址
 * @return bool true=在线, false=离线
 */
bool i2c_bus_device_online(uint8_t device_addr);

/**
 * @brief 获取I2C设备名称
 * @param device_addr 设备地址
 * @return const char* 设备名称
 */
const char* i2c_bus_get_device_name(uint8_t device_addr);

/**
 * @brief 重新扫描I2C设备
 */
void i2c_bus_rescan(void);

/**
 * @brief 反初始化I2C总线
 * @return esp_err_t 
 */
esp_err_t i2c_bus_deinit(void);

#ifdef __cplusplus
}
#endif

#endif /* I2C_BUS_H */
