/**
 * @file audio_effects.h
 * @brief TIMO音频效果器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef AUDIO_EFFECTS_H
#define AUDIO_EFFECTS_H

#include "esp_err.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

esp_err_t audio_effects_init(void);
esp_err_t audio_effects_start(void);
esp_err_t audio_effects_stop(void);
esp_err_t audio_effects_set_effect(uint8_t effect_id, bool enable);

#ifdef __cplusplus
}
#endif

#endif /* AUDIO_EFFECTS_H */
