/**
 * @file rtc_driver.c
 * @brief PCF85063 RTC时钟驱动程序
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "rtc_driver.h"
#include "hardware_hal.h"
#include "hardware_config.h"
#include "i2c_bus.h"
#include "esp_log.h"

static const char *TAG = "RTC_DRIVER";

/* PCF85063寄存器定义 */
#define PCF85063_REG_CTRL1      0x00
#define PCF85063_REG_CTRL2      0x01
#define PCF85063_REG_OFFSET     0x02
#define PCF85063_REG_RAM_BYTE   0x03
#define PCF85063_REG_SECONDS    0x04
#define PCF85063_REG_MINUTES    0x05
#define PCF85063_REG_HOURS      0x06
#define PCF85063_REG_DAYS       0x07
#define PCF85063_REG_WEEKDAYS   0x08
#define PCF85063_REG_MONTHS     0x09
#define PCF85063_REG_YEARS      0x0A

/* RTC状态 */
static bool g_rtc_initialized = false;

/**
 * @brief BCD转十进制
 */
static uint8_t bcd_to_dec(uint8_t bcd)
{
    return ((bcd >> 4) * 10) + (bcd & 0x0F);
}

/**
 * @brief 十进制转BCD
 */
static uint8_t dec_to_bcd(uint8_t dec)
{
    return ((dec / 10) << 4) | (dec % 10);
}

/**
 * @brief RTC初始化
 */
esp_err_t rtc_init(void)
{
    if (g_rtc_initialized) {
        ESP_LOGW(TAG, "RTC已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化PCF85063 RTC时钟...");
    
    if (!i2c_bus_device_online(PCF85063_I2C_ADDR)) {
        ESP_LOGE(TAG, "PCF85063设备未找到");
        return ESP_ERR_NOT_FOUND;
    }
    
    // 配置控制寄存器1
    // 设置12/24小时模式，启用时钟
    esp_err_t ret = i2c_bus_write_byte(PCF85063_I2C_ADDR, PCF85063_REG_CTRL1, 0x00);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "配置RTC控制寄存器1失败");
        return ret;
    }
    
    // 配置控制寄存器2
    ret = i2c_bus_write_byte(PCF85063_I2C_ADDR, PCF85063_REG_CTRL2, 0x00);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "配置RTC控制寄存器2失败");
        return ret;
    }
    
    g_rtc_initialized = true;
    ESP_LOGI(TAG, "PCF85063 RTC初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 设置RTC时间
 */
esp_err_t rtc_set_time(rtc_time_t *time)
{
    if (!g_rtc_initialized || !time) {
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "设置RTC时间: 20%02d-%02d-%02d %02d:%02d:%02d 星期%d",
             time->year, time->month, time->day,
             time->hour, time->minute, time->second, time->weekday);
    
    // 准备时间数据（BCD格式）
    uint8_t time_data[7] = {
        dec_to_bcd(time->second),   // 秒
        dec_to_bcd(time->minute),   // 分
        dec_to_bcd(time->hour),     // 时
        dec_to_bcd(time->day),      // 日
        time->weekday,              // 星期
        dec_to_bcd(time->month),    // 月
        dec_to_bcd(time->year)      // 年
    };
    
    // 写入时间寄存器
    esp_err_t ret = i2c_bus_write_bytes(PCF85063_I2C_ADDR, PCF85063_REG_SECONDS, 
                                       time_data, sizeof(time_data));
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置RTC时间失败");
        return ret;
    }
    
    ESP_LOGI(TAG, "RTC时间设置成功");
    return ESP_OK;
}

/**
 * @brief 读取RTC时间
 */
esp_err_t rtc_get_time(rtc_time_t *time)
{
    if (!g_rtc_initialized || !time) {
        return ESP_ERR_INVALID_STATE;
    }
    
    // 读取时间寄存器
    uint8_t time_data[7];
    esp_err_t ret = i2c_bus_read_bytes(PCF85063_I2C_ADDR, PCF85063_REG_SECONDS, 
                                      time_data, sizeof(time_data));
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "读取RTC时间失败");
        return ret;
    }
    
    // 解析时间数据（BCD转十进制）
    time->second = bcd_to_dec(time_data[0] & 0x7F);  // 清除VL位
    time->minute = bcd_to_dec(time_data[1] & 0x7F);
    time->hour = bcd_to_dec(time_data[2] & 0x3F);    // 24小时制
    time->day = bcd_to_dec(time_data[3] & 0x3F);
    time->weekday = time_data[4] & 0x07;
    time->month = bcd_to_dec(time_data[5] & 0x1F);
    time->year = bcd_to_dec(time_data[6]);
    
    ESP_LOGD(TAG, "读取RTC时间: 20%02d-%02d-%02d %02d:%02d:%02d 星期%d",
             time->year, time->month, time->day,
             time->hour, time->minute, time->second, time->weekday);
    
    return ESP_OK;
}
