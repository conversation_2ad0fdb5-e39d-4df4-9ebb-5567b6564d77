/**
 * @file system_monitor.c
 * @brief TIMO系统监控器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "system_monitor.h"
#include "esp_log.h"

static const char *TAG = "SYSTEM_MONITOR";

esp_err_t system_monitor_init(void)
{
    ESP_LOGI(TAG, "初始化系统监控器...");
    return ESP_OK;
}

esp_err_t system_monitor_start(void)
{
    ESP_LOGI(TAG, "启动系统监控器...");
    return ESP_OK;
}

esp_err_t system_monitor_stop(void)
{
    ESP_LOGI(TAG, "停止系统监控器...");
    return ESP_OK;
}
