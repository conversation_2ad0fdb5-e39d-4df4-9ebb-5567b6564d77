/**
 * @file gpio_expander.c
 * @brief TCA9554PWR GPIO扩展芯片驱动
 * @version 1.0.0
 * @date 2025-06-27
 * 
 * 功能说明：
 * - 解决ESP32-S3引脚不足问题
 * - 控制LCD_RST, TOUCH_RST, LCD_CS, SD_CS等信号
 * - 处理传感器中断信号
 * - 控制蜂鸣器
 */

#include "gpio_expander.h"
#include "hardware_config.h"
#include "esp_log.h"
#include "driver/i2c.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

static const char *TAG = "GPIO_EXPANDER";

/* TCA9554 寄存器定义 */
#define TCA9554_REG_INPUT           0x00    // 输入端口寄存器
#define TCA9554_REG_OUTPUT          0x01    // 输出端口寄存器
#define TCA9554_REG_POLARITY        0x02    // 极性反转寄存器
#define TCA9554_REG_CONFIG          0x03    // 配置寄存器

/* GPIO扩展芯片状态 */
static bool g_exio_initialized = false;
static uint8_t g_exio_output_state = 0x00;
static uint8_t g_exio_config = 0x00;
static SemaphoreHandle_t g_exio_mutex = NULL;

/* 中断处理相关 */
static gpio_isr_handle_t g_exio_isr_handle = NULL;
static TaskHandle_t g_exio_task_handle = NULL;
static QueueHandle_t g_exio_event_queue = NULL;

typedef struct {
    uint8_t pin;
    uint8_t level;
} exio_event_t;

/**
 * @brief 写TCA9554寄存器
 */
static esp_err_t tca9554_write_reg(uint8_t reg, uint8_t value)
{
    if (!g_exio_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (TCA9554_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg, true);
    i2c_master_write_byte(cmd, value, true);
    i2c_master_stop(cmd);
    
    esp_err_t ret = i2c_master_cmd_begin(I2C_MASTER_NUM, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "TCA9554写寄存器失败: 0x%02x = 0x%02x", reg, value);
    }
    
    return ret;
}

/**
 * @brief 读TCA9554寄存器
 */
static esp_err_t tca9554_read_reg(uint8_t reg, uint8_t *value)
{
    if (!g_exio_initialized || !value) {
        return ESP_ERR_INVALID_ARG;
    }
    
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (TCA9554_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg, true);
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (TCA9554_I2C_ADDR << 1) | I2C_MASTER_READ, true);
    i2c_master_read_byte(cmd, value, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    
    esp_err_t ret = i2c_master_cmd_begin(I2C_MASTER_NUM, cmd, pdMS_TO_TICKS(100));
    i2c_cmd_link_delete(cmd);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "TCA9554读寄存器失败: 0x%02x", reg);
    }
    
    return ret;
}

/**
 * @brief GPIO扩展芯片中断处理函数
 */
static void IRAM_ATTR exio_isr_handler(void *arg)
{
    exio_event_t event = {0};
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    
    // 发送事件到队列
    xQueueSendFromISR(g_exio_event_queue, &event, &xHigherPriorityTaskWoken);
    
    if (xHigherPriorityTaskWoken) {
        portYIELD_FROM_ISR();
    }
}

/**
 * @brief GPIO扩展芯片中断处理任务
 */
static void exio_interrupt_task(void *pvParameters)
{
    exio_event_t event;
    uint8_t input_state;
    
    ESP_LOGI(TAG, "GPIO扩展芯片中断处理任务启动");
    
    while (1) {
        if (xQueueReceive(g_exio_event_queue, &event, portMAX_DELAY)) {
            // 读取输入状态
            if (tca9554_read_reg(TCA9554_REG_INPUT, &input_state) == ESP_OK) {
                ESP_LOGI(TAG, "GPIO扩展芯片中断，输入状态: 0x%02x", input_state);
                
                // 处理各个引脚的中断
                for (int i = 0; i < 8; i++) {
                    if ((g_exio_config & (1 << i)) && !(input_state & (1 << i))) {
                        // 输入引脚且为低电平
                        switch (i) {
                            case EXIO5:  // QMI8658_INT2
                                ESP_LOGI(TAG, "姿态传感器中断2触发");
                                break;
                            case EXIO6:  // QMI8658_INT1
                                ESP_LOGI(TAG, "姿态传感器中断1触发");
                                break;
                            case EXIO7:  // RTC_INT
                                ESP_LOGI(TAG, "RTC时钟中断触发");
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
        }
    }
}

/**
 * @brief 初始化GPIO扩展芯片
 */
esp_err_t gpio_expander_init(void)
{
    if (g_exio_initialized) {
        ESP_LOGW(TAG, "GPIO扩展芯片已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化GPIO扩展芯片 TCA9554PWR...");
    
    // 创建互斥锁
    g_exio_mutex = xSemaphoreCreateMutex();
    if (!g_exio_mutex) {
        ESP_LOGE(TAG, "创建互斥锁失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建事件队列
    g_exio_event_queue = xQueueCreate(10, sizeof(exio_event_t));
    if (!g_exio_event_queue) {
        ESP_LOGE(TAG, "创建事件队列失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 配置GPIO扩展芯片
    // EXIO0: 保留 (输出)
    // EXIO1: LCD_RST (输出)
    // EXIO2: TOUCH_RST (输出)
    // EXIO3: LCD_CS (输出)
    // EXIO4: SD_CS (输出)
    // EXIO5: QMI8658_INT2 (输入)
    // EXIO6: QMI8658_INT1 (输入)
    // EXIO7: RTC_INT (输入)
    g_exio_config = 0xE0;  // 高3位为输入，低5位为输出
    
    esp_err_t ret = tca9554_write_reg(TCA9554_REG_CONFIG, g_exio_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "配置GPIO扩展芯片失败");
        return ret;
    }
    
    // 设置极性反转 (正常极性)
    ret = tca9554_write_reg(TCA9554_REG_POLARITY, 0x00);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置极性反转失败");
        return ret;
    }
    
    // 设置初始输出状态
    // LCD_RST=1, TOUCH_RST=1, LCD_CS=1, SD_CS=1 (高电平无效)
    g_exio_output_state = 0x1E;
    ret = tca9554_write_reg(TCA9554_REG_OUTPUT, g_exio_output_state);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置初始输出状态失败");
        return ret;
    }
    
    // 配置中断引脚
    gpio_config_t io_conf = {
        .pin_bit_mask = (1ULL << EXIO_INT_IO),
        .mode = GPIO_MODE_INPUT,
        .pull_up_en = GPIO_PULLUP_ENABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .intr_type = GPIO_INTR_NEGEDGE
    };
    gpio_config(&io_conf);
    
    // 安装中断服务
    gpio_install_isr_service(0);
    gpio_isr_handler_add(EXIO_INT_IO, exio_isr_handler, NULL);
    
    // 创建中断处理任务
    BaseType_t task_ret = xTaskCreate(
        exio_interrupt_task,
        "exio_interrupt",
        2048,
        NULL,
        5,
        &g_exio_task_handle
    );
    
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "创建中断处理任务失败");
        return ESP_ERR_NO_MEM;
    }
    
    g_exio_initialized = true;
    ESP_LOGI(TAG, "GPIO扩展芯片初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 设置GPIO扩展引脚电平
 */
esp_err_t gpio_expander_set_level(exio_pin_t pin, uint8_t level)
{
    if (!g_exio_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (pin >= 8) {
        return ESP_ERR_INVALID_ARG;
    }
    
    // 检查是否为输出引脚
    if (g_exio_config & (1 << pin)) {
        ESP_LOGE(TAG, "EXIO%d 配置为输入引脚，无法设置输出", pin);
        return ESP_ERR_INVALID_ARG;
    }
    
    xSemaphoreTake(g_exio_mutex, portMAX_DELAY);
    
    if (level) {
        g_exio_output_state |= (1 << pin);
    } else {
        g_exio_output_state &= ~(1 << pin);
    }
    
    esp_err_t ret = tca9554_write_reg(TCA9554_REG_OUTPUT, g_exio_output_state);
    
    xSemaphoreGive(g_exio_mutex);
    
    if (ret == ESP_OK) {
        ESP_LOGD(TAG, "设置EXIO%d = %d", pin, level);
    }
    
    return ret;
}

/**
 * @brief 读取GPIO扩展引脚电平
 */
esp_err_t gpio_expander_get_level(exio_pin_t pin, uint8_t *level)
{
    if (!g_exio_initialized || !level) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (pin >= 8) {
        return ESP_ERR_INVALID_ARG;
    }
    
    uint8_t input_state;
    esp_err_t ret = tca9554_read_reg(TCA9554_REG_INPUT, &input_state);
    
    if (ret == ESP_OK) {
        *level = (input_state & (1 << pin)) ? 1 : 0;
        ESP_LOGD(TAG, "读取EXIO%d = %d", pin, *level);
    }
    
    return ret;
}

/**
 * @brief 反初始化GPIO扩展芯片
 */
esp_err_t gpio_expander_deinit(void)
{
    if (!g_exio_initialized) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "反初始化GPIO扩展芯片...");
    
    // 删除中断处理任务
    if (g_exio_task_handle) {
        vTaskDelete(g_exio_task_handle);
        g_exio_task_handle = NULL;
    }
    
    // 删除中断服务
    gpio_isr_handler_remove(EXIO_INT_IO);
    gpio_uninstall_isr_service();
    
    // 删除队列和互斥锁
    if (g_exio_event_queue) {
        vQueueDelete(g_exio_event_queue);
        g_exio_event_queue = NULL;
    }
    
    if (g_exio_mutex) {
        vSemaphoreDelete(g_exio_mutex);
        g_exio_mutex = NULL;
    }
    
    g_exio_initialized = false;
    ESP_LOGI(TAG, "GPIO扩展芯片反初始化完成");
    
    return ESP_OK;
}
