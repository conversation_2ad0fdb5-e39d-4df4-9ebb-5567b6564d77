# TIMO智能闹钟项目 - 底座设备固件 (ESP32-C2)
cmake_minimum_required(VERSION 3.16)

# 设置项目名称和版本
set(PROJECT_NAME "timo_base_station")
set(PROJECT_VER "1.0.0")

# 包含ESP-IDF构建系统
include($ENV{IDF_PATH}/tools/cmake/project.cmake)

# 定义项目
project(${PROJECT_NAME})

# 设置编译选项
target_compile_options(${CMAKE_PROJECT_NAME}.elf PRIVATE
    -Wall
    -Wextra
    -Wno-unused-parameter
    -Wno-missing-field-initializers
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
