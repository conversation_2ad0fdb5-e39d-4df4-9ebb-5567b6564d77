/**
 * @file spi_bus.c
 * @brief SPI总线驱动 - 处理LCD和SD卡复用
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "spi_bus.h"
#include "hardware_config.h"
#include "gpio_expander.h"
#include "esp_log.h"
#include "driver/spi_master.h"
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"

static const char *TAG = "SPI_BUS";

/* SPI总线状态 */
static bool g_spi_initialized = false;
static SemaphoreHandle_t g_spi_mutex = NULL;
static spi_device_handle_t g_spi_device = NULL;

/**
 * @brief 初始化SPI总线
 */
esp_err_t hardware_spi_init(void)
{
    if (g_spi_initialized) {
        ESP_LOGW(TAG, "SPI总线已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化SPI总线...");
    
    // 创建互斥锁
    g_spi_mutex = xSemaphoreCreateMutex();
    if (!g_spi_mutex) {
        ESP_LOGE(TAG, "创建SPI互斥锁失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 配置SPI总线
    spi_bus_config_t buscfg = {
        .miso_io_num = SPI_MISO_IO,
        .mosi_io_num = SPI_MOSI_IO,
        .sclk_io_num = SPI_SCLK_IO,
        .quadwp_io_num = -1,
        .quadhd_io_num = -1,
        .max_transfer_sz = SPI_MAX_TRANSFER_SIZE,
    };
    
    esp_err_t ret = spi_bus_initialize(SPI_HOST_ID, &buscfg, SPI_DMA_CH_AUTO);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "SPI总线初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 配置SPI设备
    spi_device_interface_config_t devcfg = {
        .clock_speed_hz = 10 * 1000 * 1000,  // 10MHz
        .mode = 0,
        .spics_io_num = -1,  // 通过GPIO扩展芯片控制CS
        .queue_size = 7,
    };
    
    ret = spi_bus_add_device(SPI_HOST_ID, &devcfg, &g_spi_device);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "添加SPI设备失败: %s", esp_err_to_name(ret));
        spi_bus_free(SPI_HOST_ID);
        return ret;
    }
    
    g_spi_initialized = true;
    ESP_LOGI(TAG, "SPI总线初始化完成");
    
    return ESP_OK;
}

/**
 * @brief SPI传输数据
 */
esp_err_t spi_transfer(int cs_pin, uint8_t *tx_data, uint8_t *rx_data, size_t len)
{
    if (!g_spi_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (!tx_data && !rx_data) {
        return ESP_ERR_INVALID_ARG;
    }
    
    xSemaphoreTake(g_spi_mutex, portMAX_DELAY);
    
    spi_transaction_t trans = {
        .length = len * 8,  // 位长度
        .tx_buffer = tx_data,
        .rx_buffer = rx_data,
    };
    
    esp_err_t ret = spi_device_transmit(g_spi_device, &trans);
    
    xSemaphoreGive(g_spi_mutex);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "SPI传输失败: %s", esp_err_to_name(ret));
    }
    
    return ret;
}

/**
 * @brief 反初始化SPI总线
 */
esp_err_t spi_bus_deinit(void)
{
    if (!g_spi_initialized) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "反初始化SPI总线...");
    
    if (g_spi_device) {
        spi_bus_remove_device(g_spi_device);
        g_spi_device = NULL;
    }
    
    spi_bus_free(SPI_HOST_ID);
    
    if (g_spi_mutex) {
        vSemaphoreDelete(g_spi_mutex);
        g_spi_mutex = NULL;
    }
    
    g_spi_initialized = false;
    ESP_LOGI(TAG, "SPI总线反初始化完成");
    
    return ESP_OK;
}
