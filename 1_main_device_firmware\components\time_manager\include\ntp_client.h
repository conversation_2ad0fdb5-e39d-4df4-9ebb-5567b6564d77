/**
 * @file ntp_client.h
 * @brief TIMO NTP客户端头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef NTP_CLIENT_H
#define NTP_CLIENT_H

#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化NTP客户端
 * @return esp_err_t 
 */
esp_err_t ntp_client_init(void);

/**
 * @brief 同步NTP时间
 * @param server NTP服务器地址
 * @return esp_err_t 
 */
esp_err_t ntp_client_sync(const char *server);

#ifdef __cplusplus
}
#endif

#endif /* NTP_CLIENT_H */
