/**
 * @file sensor_monitor.h
 * @brief TIMO传感器监控器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef SENSOR_MONITOR_H
#define SENSOR_MONITOR_H

#include "esp_err.h"
#include "sensor_system.h"

#ifdef __cplusplus
extern "C" {
#endif

esp_err_t sensor_monitor_init(void);
esp_err_t sensor_monitor_start(void);
esp_err_t sensor_monitor_stop(void);
esp_err_t sensor_monitor_log_data(const environment_data_t *data);

#ifdef __cplusplus
}
#endif

#endif /* SENSOR_MONITOR_H */
