/**
 * @file ui_watchface.c
 * @brief TIMO表盘页面实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "ui_watchface.h"
#include "ui_theme.h"
#include "hardware_config.h"
#include "esp_log.h"
#include <stdio.h>

static const char *TAG = "UI_WATCHFACE";

/* 表盘页面对象 */
static lv_obj_t *g_watchface_page = NULL;
static lv_obj_t *g_time_label = NULL;
static lv_obj_t *g_date_label = NULL;
static lv_obj_t *g_battery_bar = NULL;
static lv_obj_t *g_battery_label = NULL;
static lv_obj_t *g_temp_label = NULL;
static lv_obj_t *g_humidity_label = NULL;
static lv_obj_t *g_center_circle = NULL;

/* 指针对象 */
static lv_obj_t *g_hour_hand = NULL;
static lv_obj_t *g_minute_hand = NULL;
static lv_obj_t *g_second_hand = NULL;

/* 刻度对象 */
static lv_obj_t *g_hour_marks[12] = {NULL};
static lv_obj_t *g_minute_marks[60] = {NULL};

/* 星期名称 */
static const char *WEEKDAY_NAMES[] = {
    "日", "一", "二", "三", "四", "五", "六"
};

/**
 * @brief 创建时钟刻度
 */
static void create_clock_marks(lv_obj_t *parent)
{
    const ui_theme_colors_t *colors = ui_theme_get_colors(ui_theme_get_current());
    
    // 创建小时刻度
    for (int i = 0; i < 12; i++) {
        g_hour_marks[i] = lv_obj_create(parent);
        lv_obj_set_size(g_hour_marks[i], 4, 20);
        lv_obj_set_style_bg_color(g_hour_marks[i], colors->primary, 0);
        lv_obj_set_style_border_width(g_hour_marks[i], 0, 0);
        lv_obj_set_style_radius(g_hour_marks[i], 2, 0);
        
        // 计算位置
        float angle = i * 30.0f * M_PI / 180.0f;  // 30度间隔
        int x = LCD_WIDTH / 2 + (LCD_WIDTH / 2 - 30) * sin(angle) - 2;
        int y = LCD_HEIGHT / 2 - (LCD_HEIGHT / 2 - 30) * cos(angle) - 10;
        lv_obj_set_pos(g_hour_marks[i], x, y);
    }
    
    // 创建分钟刻度（较小）
    for (int i = 0; i < 60; i++) {
        if (i % 5 != 0) {  // 跳过小时刻度位置
            g_minute_marks[i] = lv_obj_create(parent);
            lv_obj_set_size(g_minute_marks[i], 2, 10);
            lv_obj_set_style_bg_color(g_minute_marks[i], colors->secondary, 0);
            lv_obj_set_style_border_width(g_minute_marks[i], 0, 0);
            lv_obj_set_style_radius(g_minute_marks[i], 1, 0);
            
            // 计算位置
            float angle = i * 6.0f * M_PI / 180.0f;  // 6度间隔
            int x = LCD_WIDTH / 2 + (LCD_WIDTH / 2 - 25) * sin(angle) - 1;
            int y = LCD_HEIGHT / 2 - (LCD_HEIGHT / 2 - 25) * cos(angle) - 5;
            lv_obj_set_pos(g_minute_marks[i], x, y);
        }
    }
}

/**
 * @brief 创建时钟指针
 */
static void create_clock_hands(lv_obj_t *parent)
{
    const ui_theme_colors_t *colors = ui_theme_get_colors(ui_theme_get_current());
    
    // 时针
    g_hour_hand = lv_obj_create(parent);
    lv_obj_set_size(g_hour_hand, 6, 80);
    lv_obj_set_style_bg_color(g_hour_hand, colors->text_primary, 0);
    lv_obj_set_style_border_width(g_hour_hand, 0, 0);
    lv_obj_set_style_radius(g_hour_hand, 3, 0);
    lv_obj_set_pos(g_hour_hand, LCD_WIDTH / 2 - 3, LCD_HEIGHT / 2 - 80);
    
    // 分针
    g_minute_hand = lv_obj_create(parent);
    lv_obj_set_size(g_minute_hand, 4, 110);
    lv_obj_set_style_bg_color(g_minute_hand, colors->primary, 0);
    lv_obj_set_style_border_width(g_minute_hand, 0, 0);
    lv_obj_set_style_radius(g_minute_hand, 2, 0);
    lv_obj_set_pos(g_minute_hand, LCD_WIDTH / 2 - 2, LCD_HEIGHT / 2 - 110);
    
    // 秒针
    g_second_hand = lv_obj_create(parent);
    lv_obj_set_size(g_second_hand, 2, 120);
    lv_obj_set_style_bg_color(g_second_hand, colors->accent, 0);
    lv_obj_set_style_border_width(g_second_hand, 0, 0);
    lv_obj_set_style_radius(g_second_hand, 1, 0);
    lv_obj_set_pos(g_second_hand, LCD_WIDTH / 2 - 1, LCD_HEIGHT / 2 - 120);
    
    // 中心圆点
    g_center_circle = lv_obj_create(parent);
    lv_obj_set_size(g_center_circle, 12, 12);
    lv_obj_set_style_bg_color(g_center_circle, colors->primary, 0);
    lv_obj_set_style_border_width(g_center_circle, 0, 0);
    lv_obj_set_style_radius(g_center_circle, LV_RADIUS_CIRCLE, 0);
    lv_obj_center(g_center_circle);
}

/**
 * @brief 更新指针位置
 */
static void update_clock_hands(uint8_t hour, uint8_t minute, uint8_t second)
{
    if (!g_hour_hand || !g_minute_hand || !g_second_hand) {
        return;
    }
    
    // 计算角度
    float hour_angle = ((hour % 12) * 30 + minute * 0.5f) * M_PI / 180.0f;
    float minute_angle = minute * 6.0f * M_PI / 180.0f;
    float second_angle = second * 6.0f * M_PI / 180.0f;
    
    // 更新时针位置
    lv_obj_set_style_transform_angle(g_hour_hand, (int)(hour_angle * 180 / M_PI * 10), 0);
    
    // 更新分针位置
    lv_obj_set_style_transform_angle(g_minute_hand, (int)(minute_angle * 180 / M_PI * 10), 0);
    
    // 更新秒针位置
    lv_obj_set_style_transform_angle(g_second_hand, (int)(second_angle * 180 / M_PI * 10), 0);
}

/**
 * @brief 创建表盘页面
 */
lv_obj_t* ui_watchface_create(void)
{
    ESP_LOGI(TAG, "创建表盘页面...");
    
    // 创建页面容器
    g_watchface_page = lv_obj_create(NULL);
    lv_obj_set_size(g_watchface_page, LCD_WIDTH, LCD_HEIGHT);
    lv_obj_set_style_bg_color(g_watchface_page, lv_color_black(), 0);
    lv_obj_set_style_border_width(g_watchface_page, 0, 0);
    lv_obj_set_style_pad_all(g_watchface_page, 0, 0);
    
    // 创建时钟刻度
    create_clock_marks(g_watchface_page);
    
    // 创建时钟指针
    create_clock_hands(g_watchface_page);
    
    // 创建数字时间显示
    g_time_label = lv_label_create(g_watchface_page);
    lv_obj_add_style(g_time_label, ui_theme_create_label_style(20), 0);
    lv_label_set_text(g_time_label, "12:00");
    lv_obj_set_pos(g_time_label, LCD_WIDTH / 2 - 40, LCD_HEIGHT / 2 + 60);
    
    // 创建日期显示
    g_date_label = lv_label_create(g_watchface_page);
    lv_obj_add_style(g_date_label, ui_theme_create_label_style(14), 0);
    lv_label_set_text(g_date_label, "2025-06-27 周四");
    lv_obj_set_pos(g_date_label, LCD_WIDTH / 2 - 60, LCD_HEIGHT / 2 + 90);
    
    // 创建电池显示
    g_battery_bar = lv_bar_create(g_watchface_page);
    lv_obj_set_size(g_battery_bar, 60, 8);
    lv_obj_set_pos(g_battery_bar, LCD_WIDTH / 2 - 30, 20);
    lv_bar_set_range(g_battery_bar, 0, 100);
    lv_bar_set_value(g_battery_bar, 80, LV_ANIM_OFF);
    
    g_battery_label = lv_label_create(g_watchface_page);
    lv_obj_add_style(g_battery_label, ui_theme_create_label_style(12), 0);
    lv_label_set_text(g_battery_label, "80%");
    lv_obj_set_pos(g_battery_label, LCD_WIDTH / 2 + 40, 15);
    
    // 创建温湿度显示
    g_temp_label = lv_label_create(g_watchface_page);
    lv_obj_add_style(g_temp_label, ui_theme_create_label_style(12), 0);
    lv_label_set_text(g_temp_label, "25°C");
    lv_obj_set_pos(g_temp_label, 20, LCD_HEIGHT / 2 - 20);
    
    g_humidity_label = lv_label_create(g_watchface_page);
    lv_obj_add_style(g_humidity_label, ui_theme_create_label_style(12), 0);
    lv_label_set_text(g_humidity_label, "60%");
    lv_obj_set_pos(g_humidity_label, 20, LCD_HEIGHT / 2 + 10);
    
    ESP_LOGI(TAG, "表盘页面创建完成");
    return g_watchface_page;
}

/**
 * @brief 更新时间显示
 */
esp_err_t ui_watchface_update_time(uint8_t hour, uint8_t minute, uint8_t second)
{
    if (!g_time_label) {
        return ESP_ERR_INVALID_STATE;
    }
    
    // 更新数字时间
    char time_str[16];
    snprintf(time_str, sizeof(time_str), "%02d:%02d", hour, minute);
    lv_label_set_text(g_time_label, time_str);
    
    // 更新指针位置
    update_clock_hands(hour, minute, second);
    
    return ESP_OK;
}

/**
 * @brief 更新日期显示
 */
esp_err_t ui_watchface_update_date(uint16_t year, uint8_t month, uint8_t day, uint8_t weekday)
{
    if (!g_date_label) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (weekday > 6) {
        weekday = 0;
    }
    
    char date_str[32];
    snprintf(date_str, sizeof(date_str), "%04d-%02d-%02d 周%s", 
             year, month, day, WEEKDAY_NAMES[weekday]);
    lv_label_set_text(g_date_label, date_str);
    
    return ESP_OK;
}

/**
 * @brief 更新电池状态显示
 */
esp_err_t ui_watchface_update_battery(uint8_t level, bool charging)
{
    if (!g_battery_bar || !g_battery_label) {
        return ESP_ERR_INVALID_STATE;
    }
    
    lv_bar_set_value(g_battery_bar, level, LV_ANIM_ON);
    
    char battery_str[16];
    if (charging) {
        snprintf(battery_str, sizeof(battery_str), "%d%%⚡", level);
    } else {
        snprintf(battery_str, sizeof(battery_str), "%d%%", level);
    }
    lv_label_set_text(g_battery_label, battery_str);
    
    return ESP_OK;
}

/**
 * @brief 更新环境数据显示
 */
esp_err_t ui_watchface_update_environment(float temperature, float humidity)
{
    if (!g_temp_label || !g_humidity_label) {
        return ESP_ERR_INVALID_STATE;
    }
    
    char temp_str[16];
    snprintf(temp_str, sizeof(temp_str), "%.1f°C", temperature);
    lv_label_set_text(g_temp_label, temp_str);
    
    char humidity_str[16];
    snprintf(humidity_str, sizeof(humidity_str), "%.0f%%", humidity);
    lv_label_set_text(g_humidity_label, humidity_str);
    
    return ESP_OK;
}

/**
 * @brief 设置表盘样式
 */
esp_err_t ui_watchface_set_style(uint8_t style_id)
{
    ESP_LOGI(TAG, "设置表盘样式: %d", style_id);
    
    // TODO: 实现不同的表盘样式
    
    return ESP_OK;
}
