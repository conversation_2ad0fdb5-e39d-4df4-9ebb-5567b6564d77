# TIMO智能闹钟项目总览

## 项目架构

TIMO智能闹钟项目采用分布式架构，由4个独立的子项目组成：

### 1. 主体设备固件 (ESP32-S3)
**目录**: `1_main_device_firmware/`
**芯片**: ESP32-S3N16R8 (16MB Flash + 8MB PSRAM)
**主要功能**:
- 480×480圆形LCD显示和触摸
- 多传感器数据采集 (温湿度、光照、CO2、姿态)
- 双麦克风阵列音频处理
- 蓝牙通信 (与底座设备)
- WiFi网络连接
- SD卡存储管理
- RTC时钟管理
- 用户界面系统

**核心组件**:
- `hardware/` - 硬件抽象层
- `system/` - 系统管理
- `ui/` - 用户界面 (LVGL)
- `time_manager/` - 时间和闹钟管理
- `sensor_system/` - 传感器数据采集
- `audio_system/` - 音频录放和处理
- `bluetooth_system/` - 蓝牙通信

### 2. 底座设备固件 (ESP32-C2) ✅ **已完成**
**目录**: `2_base_station_firmware/`
**芯片**: ESP32-C2 (2MB Flash)
**状态**: 开发完成，可独立编译部署

**主要功能**:
- WS2812 RGB灯带控制 (30颗LED)
- 声音传感器监测和数据处理
- 用户按键处理 (防抖动、长按检测)
- 蓝牙BLE通信 (与主体设备)
- 氛围场景控制 (13种预设场景)
- 系统状态监控和电源管理

**核心模块**:
- `ws2812_driver.c` - WS2812 LED驱动 (支持多种特效)
- `sound_sensor.c` - 声音传感器 (ADC采集、统计分析)
- `button_handler.c` - 按键处理 (中断驱动、事件队列)
- `bluetooth_comm.c` - 蓝牙通信 (GATT服务器)
- `ambient_effects.c` - 氛围灯效 (场景管理、动画算法)
- `base_main.c` - 主程序 (任务调度、系统管理)

**氛围场景**:
- 待机、对话律动、唤醒模式、助眠模式
- 待办提醒、预警模式、专注模式
- 充电状态、配对模式等

### 3. 云端服务
**目录**: `3_cloud_service/`
**技术栈**: Node.js + Express + MongoDB
**主要功能**:
- 设备注册和认证
- 用户管理和权限控制
- 数据存储和同步
- API服务接口
- 语音处理服务
- 消息推送服务
- OTA固件更新

**核心服务**:
- 设备管理服务
- 数据同步服务
- 用户认证服务
- 语音处理服务
- 通知推送服务

### 4. 微信小程序
**目录**: `4_wechat_miniprogram/`
**技术栈**: 微信小程序原生框架 + Vant UI
**主要功能**:
- 设备连接和控制
- 实时数据展示
- 闹钟设置管理
- 氛围灯效控制
- 历史数据查询
- 用户设置管理

**核心页面**:
- 首页 - 设备状态和快捷操作
- 设备页 - 设备连接和控制
- 数据页 - 环境数据和统计
- 设置页 - 用户设置和配置

## 通信架构

```
[微信小程序] ←→ [云端服务] ←→ [主体设备(WiFi)]
                                    ↓ (蓝牙)
                              [底座设备]
```

### 通信协议
1. **小程序 ↔ 云端**: HTTPS + WebSocket
2. **云端 ↔ 主体设备**: HTTPS + MQTT
3. **主体设备 ↔ 底座设备**: 蓝牙5.0 (自定义协议)

## 开发状态

### ✅ 已完成
- [x] 项目架构设计和目录结构
- [x] 主体设备硬件抽象层
- [x] 底座设备基础固件
- [x] 系统核心服务框架
- [x] 项目文档和说明

### 🚧 进行中
- [ ] 主体设备时间管理功能
- [ ] 主体设备用户界面系统
- [ ] 主体设备传感器数据采集
- [ ] 主体设备音频系统
- [ ] 主体设备蓝牙通信系统

### 📋 待开发
- [ ] 云端服务API开发
- [ ] 云端设备管理系统
- [ ] 微信小程序界面开发
- [ ] 微信小程序设备通信
- [ ] 存储管理系统
- [ ] 系统集成测试

## 开发环境

### 固件开发
- **ESP-IDF**: v5.0+
- **编译器**: GCC
- **调试工具**: ESP32 JTAG
- **IDE**: VS Code + ESP-IDF插件

### 云端开发
- **Node.js**: v16+
- **数据库**: MongoDB v4.4+
- **缓存**: Redis v6.0+
- **部署**: Docker + PM2

### 小程序开发
- **微信开发者工具**: 最新版
- **UI框架**: Vant Weapp
- **图表库**: ECharts

## 部署架构

### 开发环境
- 本地ESP32开发板
- 本地Node.js服务
- 微信开发者工具

### 生产环境
- 云服务器 (阿里云/腾讯云)
- MongoDB Atlas
- Redis Cloud
- 微信小程序发布

## 质量保证

### 代码规范
- ESP-IDF编码规范
- JavaScript Standard Style
- 微信小程序开发规范

### 测试策略
- 单元测试
- 集成测试
- 硬件在环测试
- 用户验收测试

### 文档管理
- API文档 (Swagger)
- 硬件接口文档
- 用户使用手册
- 开发者指南

## 版本管理

### 版本号规则
- 主版本号.次版本号.修订号
- 例如: 1.0.0, 1.1.0, 1.1.1

### 发布流程
1. 功能开发和测试
2. 代码审查
3. 版本标记
4. 构建和打包
5. 部署发布
6. 用户通知

## 项目里程碑

### Phase 1: 基础功能 (当前)
- 硬件驱动开发
- 基础系统服务
- 核心功能实现

### Phase 2: 完整功能
- 所有功能模块完成
- 云端服务上线
- 小程序发布

### Phase 3: 优化完善
- 性能优化
- 用户体验改进
- 功能扩展

### Phase 4: 商业化
- 产品化包装
- 市场推广
- 用户支持
