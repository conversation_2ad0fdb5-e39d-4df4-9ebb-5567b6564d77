/**
 * @file voice_processor.h
 * @brief TIMO语音处理器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef VOICE_PROCESSOR_H
#define VOICE_PROCESSOR_H

#include "esp_err.h"
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

esp_err_t voice_processor_init(void);
esp_err_t voice_processor_start(void);
esp_err_t voice_processor_stop(void);
float voice_processor_get_level(void);
bool voice_processor_voice_detected(void);

#ifdef __cplusplus
}
#endif

#endif /* VOICE_PROCESSOR_H */
