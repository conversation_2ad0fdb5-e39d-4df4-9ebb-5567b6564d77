/**
 * @file app_main.h
 * @brief TIMO智能闹钟应用程序主头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef APP_MAIN_H
#define APP_MAIN_H

#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 系统配置 */
#define APP_VERSION "1.0.0"
#define APP_NAME "TIMO智能闹钟"

/* 任务优先级定义 */
#define TASK_PRIORITY_HIGH      (configMAX_PRIORITIES - 1)
#define TASK_PRIORITY_NORMAL    (configMAX_PRIORITIES - 2)
#define TASK_PRIORITY_LOW       (configMAX_PRIORITIES - 3)

/* 任务堆栈大小定义 */
#define TASK_STACK_SIZE_LARGE   (8192)
#define TASK_STACK_SIZE_MEDIUM  (4096)
#define TASK_STACK_SIZE_SMALL   (2048)

/* 系统事件位定义 */
#define SYSTEM_EVENT_WIFI_CONNECTED     BIT0
#define SYSTEM_EVENT_TIME_SYNCED        BIT1
#define SYSTEM_EVENT_BLUETOOTH_READY    BIT2
#define SYSTEM_EVENT_SENSORS_READY      BIT3
#define SYSTEM_EVENT_AUDIO_READY        BIT4
#define SYSTEM_EVENT_UI_READY           BIT5

/* 全局事件组 */
extern EventGroupHandle_t g_system_event_group;

/* 全局队列 */
extern QueueHandle_t g_sensor_data_queue;
extern QueueHandle_t g_audio_event_queue;
extern QueueHandle_t g_ui_event_queue;

/**
 * @brief 应用程序主启动函数
 * @return esp_err_t 
 */
esp_err_t app_main_start(void);

/**
 * @brief 获取系统运行时间（毫秒）
 * @return uint64_t 系统运行时间
 */
uint64_t app_get_uptime_ms(void);

/**
 * @brief 系统重启
 */
void app_system_restart(void);

/**
 * @brief 进入深度睡眠模式
 * @param sleep_time_us 睡眠时间（微秒）
 */
void app_enter_deep_sleep(uint64_t sleep_time_us);

#ifdef __cplusplus
}
#endif

#endif /* APP_MAIN_H */
