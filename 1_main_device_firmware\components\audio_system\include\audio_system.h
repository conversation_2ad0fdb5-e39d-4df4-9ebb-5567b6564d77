/**
 * @file audio_system.h
 * @brief TIMO音频系统头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef AUDIO_SYSTEM_H
#define AUDIO_SYSTEM_H

#include "esp_err.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 音频格式定义 */
typedef enum {
    AUDIO_FORMAT_PCM = 0,       // PCM格式
    AUDIO_FORMAT_MP3,           // MP3格式
    AUDIO_FORMAT_WAV,           // WAV格式
    AUDIO_FORMAT_AAC,           // AAC格式
    AUDIO_FORMAT_MAX
} audio_format_t;

/* 音频状态定义 */
typedef enum {
    AUDIO_STATE_IDLE = 0,       // 空闲
    AUDIO_STATE_PLAYING,        // 播放中
    AUDIO_STATE_RECORDING,      // 录音中
    AUDIO_STATE_PAUSED,         // 暂停
    AUDIO_STATE_ERROR,          // 错误
    AUDIO_STATE_MAX
} audio_state_t;

/* 音频配置结构体 */
typedef struct {
    uint32_t sample_rate;       // 采样率
    uint8_t bits_per_sample;    // 位深度
    uint8_t channels;           // 声道数
    uint8_t volume;             // 音量 (0-100)
    bool auto_gain_control;     // 自动增益控制
    bool noise_suppression;     // 噪声抑制
    bool echo_cancellation;     // 回声消除
} audio_config_t;

/* 音频事件类型 */
typedef enum {
    AUDIO_EVENT_PLAY_START = 0, // 开始播放
    AUDIO_EVENT_PLAY_STOP,      // 停止播放
    AUDIO_EVENT_PLAY_PAUSE,     // 暂停播放
    AUDIO_EVENT_PLAY_RESUME,    // 恢复播放
    AUDIO_EVENT_PLAY_FINISH,    // 播放完成
    AUDIO_EVENT_RECORD_START,   // 开始录音
    AUDIO_EVENT_RECORD_STOP,    // 停止录音
    AUDIO_EVENT_VOLUME_CHANGE,  // 音量变化
    AUDIO_EVENT_ERROR,          // 错误事件
    AUDIO_EVENT_MAX
} audio_event_t;

/* 音频事件回调函数类型 */
typedef void (*audio_event_callback_t)(audio_event_t event, void *data);

/**
 * @brief 初始化音频系统
 * @return esp_err_t 
 */
esp_err_t audio_system_init(void);

/**
 * @brief 启动音频系统
 * @return esp_err_t 
 */
esp_err_t audio_system_start(void);

/**
 * @brief 停止音频系统
 * @return esp_err_t 
 */
esp_err_t audio_system_stop(void);

/**
 * @brief 播放音频文件
 * @param file_path 文件路径
 * @return esp_err_t 
 */
esp_err_t audio_system_play_file(const char *file_path);

/**
 * @brief 播放音频数据
 * @param data 音频数据
 * @param size 数据大小
 * @param format 音频格式
 * @return esp_err_t 
 */
esp_err_t audio_system_play_data(const uint8_t *data, size_t size, audio_format_t format);

/**
 * @brief 停止播放
 * @return esp_err_t 
 */
esp_err_t audio_system_stop_play(void);

/**
 * @brief 暂停播放
 * @return esp_err_t 
 */
esp_err_t audio_system_pause_play(void);

/**
 * @brief 恢复播放
 * @return esp_err_t 
 */
esp_err_t audio_system_resume_play(void);

/**
 * @brief 开始录音
 * @param file_path 录音文件路径
 * @param duration_ms 录音时长(毫秒)，0表示无限录音
 * @return esp_err_t 
 */
esp_err_t audio_system_start_record(const char *file_path, uint32_t duration_ms);

/**
 * @brief 停止录音
 * @return esp_err_t 
 */
esp_err_t audio_system_stop_record(void);

/**
 * @brief 设置音量
 * @param volume 音量 (0-100)
 * @return esp_err_t 
 */
esp_err_t audio_system_set_volume(uint8_t volume);

/**
 * @brief 获取音量
 * @return uint8_t 音量值
 */
uint8_t audio_system_get_volume(void);

/**
 * @brief 设置音频配置
 * @param config 音频配置
 * @return esp_err_t 
 */
esp_err_t audio_system_set_config(const audio_config_t *config);

/**
 * @brief 获取音频配置
 * @return audio_config_t* 
 */
audio_config_t* audio_system_get_config(void);

/**
 * @brief 获取音频状态
 * @return audio_state_t 
 */
audio_state_t audio_system_get_state(void);

/**
 * @brief 注册音频事件回调
 * @param callback 回调函数
 * @return esp_err_t 
 */
esp_err_t audio_system_register_callback(audio_event_callback_t callback);

/**
 * @brief 播放提示音
 * @param tone_id 提示音ID
 * @return esp_err_t 
 */
esp_err_t audio_system_play_tone(uint8_t tone_id);

/**
 * @brief 播放TTS语音
 * @param text 文本内容
 * @return esp_err_t 
 */
esp_err_t audio_system_play_tts(const char *text);

/**
 * @brief 获取音频电平
 * @return float 音频电平 (0.0-1.0)
 */
float audio_system_get_level(void);

/**
 * @brief 检测语音活动
 * @return bool true=检测到语音, false=无语音
 */
bool audio_system_voice_detected(void);

/**
 * @brief 启用/禁用音频效果
 * @param effect_id 效果ID
 * @param enable 是否启用
 * @return esp_err_t 
 */
esp_err_t audio_system_set_effect(uint8_t effect_id, bool enable);

#ifdef __cplusplus
}
#endif

#endif /* AUDIO_SYSTEM_H */
