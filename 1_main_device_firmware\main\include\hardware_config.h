/**
 * @file hardware_config.h
 * @brief TIMO智能闹钟硬件配置定义
 * @version 1.0.0
 * @date 2025-06-27
 * 
 * 引脚分配说明：
 * 1. I2C总线共享：GPIO7(SCL) + GPIO15(SDA) 用于所有I2C设备
 * 2. SPI接口复用：LCD和SD卡共享SPI接口，通过CS片选区分
 * 3. 音频I2S接口：ES7210和ES8311共享I2S总线
 * 4. GPIO扩展：通过TCA9554PWR扩展8个GPIO(EXIO0-EXIO7)
 */

#ifndef HARDWARE_CONFIG_H
#define HARDWARE_CONFIG_H

#include "driver/gpio.h"
#include "driver/i2c.h"
#include "driver/spi_master.h"
#include "driver/i2s.h"
#include "driver/adc.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ========== GPIO引脚定义 ========== */

/* I2C总线配置 - 所有I2C设备共享 */
#define I2C_MASTER_NUM              I2C_NUM_0
#define I2C_MASTER_SCL_IO           7
#define I2C_MASTER_SDA_IO           15
#define I2C_MASTER_FREQ_HZ          400000
#define I2C_MASTER_TX_BUF_DISABLE   0
#define I2C_MASTER_RX_BUF_DISABLE   0
#define I2C_MASTER_TIMEOUT_MS       1000

/* SPI总线配置 - LCD和SD卡共享 */
#define SPI_HOST_ID                 SPI2_HOST
#define SPI_MOSI_IO                 1
#define SPI_MISO_IO                 42  // 仅SD卡使用
#define SPI_SCLK_IO                 2
#define SPI_MAX_TRANSFER_SIZE       4096

/* LCD显示屏配置 */
#define LCD_BL_IO                   6       // 背光控制
#define LCD_RST_IO                  -1      // 通过EXIO1控制
#define LCD_CS_IO                   -1      // 通过EXIO3控制
#define LCD_WIDTH                   480
#define LCD_HEIGHT                  480
#define LCD_PIXEL_FORMAT            16      // RGB565

/* LCD RGB接口配置 */
#define LCD_PCLK_IO                 41
#define LCD_DE_IO                   40
#define LCD_VSYNC_IO                39
#define LCD_HSYNC_IO                38
#define LCD_B1_IO                   5
#define LCD_B2_IO                   45
#define LCD_B3_IO                   48
#define LCD_B4_IO                   47
#define LCD_B5_IO                   21
#define LCD_G0_IO                   14
#define LCD_G1_IO                   13
#define LCD_G2_IO                   12
#define LCD_G3_IO                   11
#define LCD_G4_IO                   10
#define LCD_G5_IO                   9
#define LCD_R1_IO                   46
#define LCD_R2_IO                   3
#define LCD_R3_IO                   8
#define LCD_R4_IO                   18
#define LCD_R5_IO                   17

/* 触摸屏配置 */
#define TOUCH_I2C_ADDR              0x5D
#define TOUCH_INT_IO                16
#define TOUCH_RST_IO                -1      // 通过EXIO2控制

/* SD卡配置 */
#define SD_CS_IO                    -1      // 通过EXIO4控制
#define SD_DETECT_IO                -1      // 可选检测引脚

/* I2S音频接口配置 - ES7210和ES8311共享 */
#define I2S_NUM                     I2S_NUM_0
#define I2S_MCK_IO                  35
#define I2S_BCK_IO                  36
#define I2S_WS_IO                   37
#define I2S_DI_IO                   19      // ES7210数据输入
#define I2S_DO_IO                   20      // ES8311数据输出

/* 电池电压检测 */
#define BAT_ADC_CHANNEL             ADC1_CHANNEL_3  // GPIO4
#define BAT_ADC_UNIT                ADC_UNIT_1
#define BAT_ADC_ATTEN               ADC_ATTEN_DB_11

/* GPIO扩展芯片中断 */
#define EXIO_INT_IO                 0

/* ========== I2C设备地址定义 ========== */
#define TCA9554_I2C_ADDR            0x20    // GPIO扩展芯片
#define BH1750_I2C_ADDR             0x23    // 光照传感器
#define AHT30_I2C_ADDR              0x38    // 温湿度传感器
#define ES7210_I2C_ADDR             0x41    // 音频输入
#define PCF85063_I2C_ADDR           0x51    // RTC时钟
#define SGP30_I2C_ADDR              0x58    // CO2传感器
#define TOUCH_GT911_I2C_ADDR        0x5D    // 触摸屏
#define QMI8658_I2C_ADDR            0x6A    // 姿态传感器
#define ES8311_I2C_ADDR             0x18    // 音频输出

/* ========== GPIO扩展引脚定义 ========== */
typedef enum {
    EXIO0 = 0,      // 保留
    EXIO1,          // LCD_RST
    EXIO2,          // TOUCH_RST
    EXIO3,          // LCD_CS
    EXIO4,          // SD_CS
    EXIO5,          // QMI8658_INT2
    EXIO6,          // QMI8658_INT1
    EXIO7,          // RTC_INT
    EXIO8           // 蜂鸣器控制（注意：文档中显示为EXIO8，但TCA9554只有8个引脚0-7）
} exio_pin_t;

/* ========== 硬件配置结构体 ========== */
typedef struct {
    bool i2c_initialized;
    bool spi_initialized;
    bool i2s_initialized;
    bool lcd_initialized;
    bool touch_initialized;
    bool sd_initialized;
    bool sensors_initialized;
    bool audio_initialized;
    bool exio_initialized;
} hardware_status_t;

/* ========== 函数声明 ========== */

/**
 * @brief 初始化I2C总线
 * @return esp_err_t 
 */
esp_err_t hardware_i2c_init(void);

/**
 * @brief 初始化SPI总线
 * @return esp_err_t 
 */
esp_err_t hardware_spi_init(void);

/**
 * @brief 初始化I2S音频接口
 * @return esp_err_t 
 */
esp_err_t hardware_i2s_init(void);

/**
 * @brief 初始化GPIO扩展芯片
 * @return esp_err_t 
 */
esp_err_t hardware_exio_init(void);

/**
 * @brief 控制GPIO扩展引脚
 * @param pin 扩展引脚编号
 * @param level 电平状态
 * @return esp_err_t 
 */
esp_err_t hardware_exio_set_level(exio_pin_t pin, uint8_t level);

/**
 * @brief 读取GPIO扩展引脚状态
 * @param pin 扩展引脚编号
 * @param level 读取到的电平状态
 * @return esp_err_t 
 */
esp_err_t hardware_exio_get_level(exio_pin_t pin, uint8_t *level);

/**
 * @brief 获取硬件状态
 * @return hardware_status_t* 
 */
hardware_status_t* hardware_get_status(void);

#ifdef __cplusplus
}
#endif

#endif /* HARDWARE_CONFIG_H */
