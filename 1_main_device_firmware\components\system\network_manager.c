/**
 * @file network_manager.c
 * @brief TIMO网络管理器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "network_manager.h"
#include "esp_log.h"

static const char *TAG = "NETWORK_MANAGER";

static bool g_wifi_connected = false;
static bool g_bluetooth_connected = false;
static int8_t g_wifi_rssi = -100;

esp_err_t network_manager_init(void)
{
    ESP_LOGI(TAG, "初始化网络管理器...");
    return ESP_OK;
}

esp_err_t network_manager_start_wifi(void)
{
    ESP_LOGI(TAG, "启动WiFi...");
    return ESP_OK;
}

esp_err_t network_manager_start_bluetooth(void)
{
    ESP_LOGI(TAG, "启动蓝牙...");
    return ESP_OK;
}

esp_err_t network_manager_stop(void)
{
    ESP_LOGI(TAG, "停止网络管理器...");
    return ESP_OK;
}

bool network_manager_is_wifi_connected(void)
{
    return g_wifi_connected;
}

bool network_manager_is_bluetooth_connected(void)
{
    return g_bluetooth_connected;
}

int8_t network_manager_get_wifi_rssi(void)
{
    return g_wifi_rssi;
}
