/**
 * @file network_manager.c
 * @brief TIMO网络管理器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "network_manager.h"
#include "bluetooth_system.h"
#include "bt_scene_sync.h"
#include "esp_log.h"

static const char *TAG = "NETWORK_MANAGER";

static bool g_wifi_connected = false;
static bool g_bluetooth_connected = false;
static int8_t g_wifi_rssi = -100;
static bool g_bluetooth_initialized = false;

/**
 * @brief 蓝牙事件回调
 */
static void bluetooth_event_callback(bt_event_type_t event, void *data)
{
    switch (event) {
        case BT_EVENT_CONNECTED:
            g_bluetooth_connected = true;
            ESP_LOGI(TAG, "蓝牙已连接到底座设备");
            break;

        case BT_EVENT_DISCONNECTED:
            g_bluetooth_connected = false;
            ESP_LOGI(TAG, "蓝牙与底座设备断开连接");
            break;

        case BT_EVENT_SCAN_COMPLETE:
            ESP_LOGI(TAG, "蓝牙扫描完成");
            break;

        default:
            break;
    }
}

esp_err_t network_manager_init(void)
{
    ESP_LOGI(TAG, "初始化网络管理器...");

    // 初始化蓝牙系统
    esp_err_t ret = bluetooth_system_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "蓝牙系统初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 初始化场景同步模块
    ret = bt_scene_sync_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "场景同步模块初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 注册蓝牙事件回调
    bluetooth_system_register_event_callback(bluetooth_event_callback);

    g_bluetooth_initialized = true;
    ESP_LOGI(TAG, "网络管理器初始化完成");
    return ESP_OK;
}

esp_err_t network_manager_start_wifi(void)
{
    ESP_LOGI(TAG, "启动WiFi...");
    // TODO: 实现WiFi功能
    return ESP_OK;
}

esp_err_t network_manager_start_bluetooth(void)
{
    if (!g_bluetooth_initialized) {
        ESP_LOGE(TAG, "蓝牙系统未初始化");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "启动蓝牙系统...");

    // 启动蓝牙系统
    esp_err_t ret = bluetooth_system_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "蓝牙系统启动失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 开始扫描底座设备
    ret = bluetooth_system_start_scan(30); // 30秒超时
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "蓝牙扫描启动失败: %s", esp_err_to_name(ret));
    }

    ESP_LOGI(TAG, "蓝牙系统启动完成");
    return ESP_OK;
}

esp_err_t network_manager_stop(void)
{
    ESP_LOGI(TAG, "停止网络管理器...");

    if (g_bluetooth_initialized) {
        // 停止蓝牙系统
        bluetooth_system_stop();
        bt_scene_sync_deinit();
        bluetooth_system_deinit();
        g_bluetooth_initialized = false;
    }

    g_bluetooth_connected = false;
    return ESP_OK;
}

bool network_manager_is_wifi_connected(void)
{
    return g_wifi_connected;
}

bool network_manager_is_bluetooth_connected(void)
{
    return g_bluetooth_connected;
}

int8_t network_manager_get_wifi_rssi(void)
{
    return g_wifi_rssi;
}

/**
 * @brief 连接到指定的底座设备
 */
esp_err_t network_manager_connect_to_base(const esp_bd_addr_t device_addr)
{
    if (!g_bluetooth_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "连接到底座设备");
    return bluetooth_system_connect(device_addr);
}

/**
 * @brief 断开与底座设备的连接
 */
esp_err_t network_manager_disconnect_from_base(void)
{
    if (!g_bluetooth_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "断开与底座设备的连接");
    return bluetooth_system_disconnect();
}

/**
 * @brief 获取扫描到的底座设备列表
 */
uint8_t network_manager_get_base_devices(bt_device_info_t *devices, uint8_t max_count)
{
    if (!g_bluetooth_initialized) {
        return 0;
    }

    return bluetooth_system_get_scan_results(devices, max_count);
}

/**
 * @brief 重新扫描底座设备
 */
esp_err_t network_manager_rescan_base_devices(void)
{
    if (!g_bluetooth_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "重新扫描底座设备");
    return bluetooth_system_start_scan(30);
}

/**
 * @brief 获取蓝牙连接状态
 */
bt_connection_state_t network_manager_get_bluetooth_state(void)
{
    if (!g_bluetooth_initialized) {
        return BT_STATE_IDLE;
    }

    return bluetooth_system_get_state();
}
