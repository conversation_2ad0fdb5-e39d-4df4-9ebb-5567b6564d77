/**
 * @file bt_test.c
 * @brief TIMO蓝牙系统测试程序
 * @version 1.0.0
 * @date 2025-06-28
 */

#include "bluetooth_system.h"
#include "bt_scene_sync.h"
#include "bt_protocol.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char *TAG = "BT_TEST";

/* 测试状态 */
static bool g_test_running = false;
static bool g_device_found = false;
static bt_device_info_t g_target_device = {0};

/**
 * @brief 蓝牙事件回调
 */
static void test_bt_event_callback(bt_event_type_t event, void *data)
{
    switch (event) {
        case BT_EVENT_SCAN_START:
            ESP_LOGI(TAG, "开始扫描设备");
            break;
            
        case BT_EVENT_SCAN_RESULT:
            if (data) {
                bt_device_info_t *device = (bt_device_info_t*)data;
                ESP_LOGI(TAG, "发现设备: %s, RSSI: %d, 目标设备: %s", 
                         device->name, device->rssi, device->is_target ? "是" : "否");
                
                if (device->is_target && !g_device_found) {
                    memcpy(&g_target_device, device, sizeof(bt_device_info_t));
                    g_device_found = true;
                    ESP_LOGI(TAG, "找到目标设备，准备连接");
                }
            }
            break;
            
        case BT_EVENT_SCAN_COMPLETE:
            ESP_LOGI(TAG, "扫描完成");
            if (g_device_found) {
                ESP_LOGI(TAG, "开始连接到目标设备: %s", g_target_device.name);
                bluetooth_system_connect(g_target_device.addr);
            } else {
                ESP_LOGW(TAG, "未找到目标设备");
            }
            break;
            
        case BT_EVENT_CONNECTED:
            ESP_LOGI(TAG, "已连接到底座设备");
            break;
            
        case BT_EVENT_DISCONNECTED:
            ESP_LOGI(TAG, "与底座设备断开连接");
            g_device_found = false;
            break;
            
        case BT_EVENT_ERROR:
            ESP_LOGE(TAG, "蓝牙事件错误");
            break;
            
        default:
            break;
    }
}

/**
 * @brief 蓝牙数据回调
 */
static void test_bt_data_callback(const bt_data_packet_t *packet)
{
    if (!packet) {
        return;
    }
    
    ESP_LOGI(TAG, "收到数据包: type=0x%02X, cmd=0x%02X, len=%d", 
             packet->type, packet->cmd, packet->length);
    
    switch (packet->cmd) {
        case BT_CMD_PONG:
            ESP_LOGI(TAG, "收到心跳响应");
            break;
            
        case BT_CMD_GET_STATUS:
            if (packet->length >= sizeof(bt_device_status_t)) {
                bt_device_status_t *status = (bt_device_status_t*)packet->data;
                ESP_LOGI(TAG, "底座状态: LED=%d, 场景=%d, 亮度=%d, 声音=%d", 
                         status->led_status, status->current_scene, 
                         status->brightness, status->sound_level);
            }
            break;
            
        default:
            ESP_LOGD(TAG, "未处理的数据包命令: 0x%02X", packet->cmd);
            break;
    }
}

/**
 * @brief 场景同步回调
 */
static void test_scene_sync_callback(bt_scene_id_t scene_id, bool success)
{
    ESP_LOGI(TAG, "场景同步回调: 场景=%d, 成功=%s", scene_id, success ? "是" : "否");
}

/**
 * @brief 测试场景同步功能
 */
static void test_scene_sync_functions(void)
{
    if (!bluetooth_system_is_connected()) {
        ESP_LOGW(TAG, "设备未连接，跳过场景测试");
        return;
    }
    
    ESP_LOGI(TAG, "开始测试场景同步功能");
    
    // 测试待机模式
    ESP_LOGI(TAG, "测试待机模式");
    bt_scene_sync_standby(50);
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 测试对话律动
    ESP_LOGI(TAG, "测试对话律动");
    bt_scene_sync_conversation();
    vTaskDelay(pdMS_TO_TICKS(3000));
    
    // 测试晨间唤醒
    ESP_LOGI(TAG, "测试晨间唤醒");
    bt_scene_sync_morning_wake();
    vTaskDelay(pdMS_TO_TICKS(5000));
    
    // 测试助眠模式
    ESP_LOGI(TAG, "测试助眠模式");
    bt_scene_sync_sleep();
    vTaskDelay(pdMS_TO_TICKS(3000));
    
    // 测试自定义颜色
    ESP_LOGI(TAG, "测试自定义颜色");
    bt_scene_sync_set_custom_color(255, 0, 0, 80); // 红色
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    bt_scene_sync_set_custom_color(0, 255, 0, 80); // 绿色
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    bt_scene_sync_set_custom_color(0, 0, 255, 80); // 蓝色
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 测试预警模式
    ESP_LOGI(TAG, "测试预警模式");
    bt_scene_sync_warning(0); // 低级预警
    vTaskDelay(pdMS_TO_TICKS(3000));
    
    bt_scene_sync_warning(1); // 中级预警
    vTaskDelay(pdMS_TO_TICKS(3000));
    
    bt_scene_sync_warning(2); // 高级预警
    vTaskDelay(pdMS_TO_TICKS(3000));
    
    // 回到待机模式
    ESP_LOGI(TAG, "回到待机模式");
    bt_scene_sync_standby(30);
    
    ESP_LOGI(TAG, "场景同步功能测试完成");
}

/**
 * @brief 测试协议功能
 */
static void test_protocol_functions(void)
{
    ESP_LOGI(TAG, "开始测试协议功能");
    
    // 测试数据包创建和解析
    uint8_t packet_buffer[256];
    uint8_t test_data[] = {0x01, 0x02, 0x03, 0x04};
    
    // 创建测试数据包
    uint16_t packet_len = bt_protocol_create_packet(BT_DATA_TYPE_CMD, BT_CMD_PING, 
                                                   test_data, sizeof(test_data),
                                                   packet_buffer, sizeof(packet_buffer));
    
    if (packet_len > 0) {
        ESP_LOGI(TAG, "数据包创建成功，长度: %d", packet_len);
        
        // 解析数据包
        bt_packet_header_t header;
        const uint8_t *payload;
        uint16_t payload_len;
        
        if (bt_protocol_parse_packet(packet_buffer, packet_len, &header, &payload, &payload_len)) {
            ESP_LOGI(TAG, "数据包解析成功: type=0x%02X, cmd=0x%02X, len=%d", 
                     header.type, header.cmd, header.length);
            
            if (payload_len == sizeof(test_data) && memcmp(payload, test_data, payload_len) == 0) {
                ESP_LOGI(TAG, "数据包内容验证成功");
            } else {
                ESP_LOGE(TAG, "数据包内容验证失败");
            }
        } else {
            ESP_LOGE(TAG, "数据包解析失败");
        }
    } else {
        ESP_LOGE(TAG, "数据包创建失败");
    }
    
    ESP_LOGI(TAG, "协议功能测试完成");
}

/**
 * @brief 蓝牙系统测试任务
 */
static void bluetooth_test_task(void *pvParameters)
{
    ESP_LOGI(TAG, "蓝牙系统测试任务启动");
    
    // 等待系统稳定
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 测试协议功能
    test_protocol_functions();
    
    // 初始化蓝牙系统
    ESP_LOGI(TAG, "初始化蓝牙系统");
    esp_err_t ret = bluetooth_system_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "蓝牙系统初始化失败: %s", esp_err_to_name(ret));
        goto cleanup;
    }
    
    // 初始化场景同步
    ret = bt_scene_sync_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "场景同步初始化失败: %s", esp_err_to_name(ret));
        goto cleanup;
    }
    
    // 注册回调函数
    bluetooth_system_register_event_callback(test_bt_event_callback);
    bluetooth_system_register_data_callback(test_bt_data_callback);
    bt_scene_sync_register_callback(test_scene_sync_callback);
    
    // 启动蓝牙系统
    ESP_LOGI(TAG, "启动蓝牙系统");
    ret = bluetooth_system_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "蓝牙系统启动失败: %s", esp_err_to_name(ret));
        goto cleanup;
    }
    
    // 开始扫描
    ESP_LOGI(TAG, "开始扫描底座设备");
    ret = bluetooth_system_start_scan(30);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "扫描启动失败: %s", esp_err_to_name(ret));
        goto cleanup;
    }
    
    g_test_running = true;
    
    // 主测试循环
    uint32_t test_cycle = 0;
    while (g_test_running) {
        vTaskDelay(pdMS_TO_TICKS(10000)); // 10秒
        test_cycle++;
        
        ESP_LOGI(TAG, "测试周期: %d", test_cycle);
        
        if (bluetooth_system_is_connected()) {
            // 发送心跳包
            bluetooth_system_send_command(BT_CMD_PING, NULL, 0);
            
            // 获取底座状态
            bluetooth_system_send_command(BT_CMD_GET_STATUS, NULL, 0);
            
            // 每5个周期测试一次场景同步
            if (test_cycle % 5 == 0) {
                test_scene_sync_functions();
            }
        } else {
            ESP_LOGW(TAG, "设备未连接，尝试重新扫描");
            if (!g_device_found) {
                bluetooth_system_start_scan(30);
            }
        }
        
        // 运行30个周期后停止测试
        if (test_cycle >= 30) {
            ESP_LOGI(TAG, "测试完成，停止测试");
            g_test_running = false;
        }
    }
    
cleanup:
    // 清理资源
    bluetooth_system_stop();
    bt_scene_sync_deinit();
    bluetooth_system_deinit();
    
    ESP_LOGI(TAG, "蓝牙系统测试任务结束");
    vTaskDelete(NULL);
}

/**
 * @brief 启动蓝牙系统测试
 */
esp_err_t bt_test_start(void)
{
    ESP_LOGI(TAG, "启动蓝牙系统测试");
    
    BaseType_t ret = xTaskCreate(
        bluetooth_test_task,
        "bt_test",
        8192,
        NULL,
        5,
        NULL
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建测试任务失败");
        return ESP_ERR_NO_MEM;
    }
    
    return ESP_OK;
}

/**
 * @brief 停止蓝牙系统测试
 */
void bt_test_stop(void)
{
    ESP_LOGI(TAG, "停止蓝牙系统测试");
    g_test_running = false;
}
