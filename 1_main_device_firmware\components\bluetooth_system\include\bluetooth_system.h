/**
 * @file bluetooth_system.h
 * @brief TIMO主体设备蓝牙通信系统头文件
 * @version 1.0.0
 * @date 2025-06-28
 */

#ifndef BLUETOOTH_SYSTEM_H
#define BLUETOOTH_SYSTEM_H

#include "esp_err.h"
#include "esp_bt.h"
#include "esp_gap_ble_api.h"
#include "esp_gattc_api.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 蓝牙配置常量 */
#define BT_DEVICE_NAME              "TIMO_Main"
#define BT_BASE_DEVICE_NAME         "TIMO_Base"
#define BT_SCAN_TIMEOUT_S           30
#define BT_CONNECT_TIMEOUT_S        10
#define BT_MAX_DATA_LENGTH          512

/* 服务和特征值UUID (与底座保持一致) */
#define BT_SERVICE_UUID_128         {0xbc, 0x9a, 0x78, 0x56, 0x34, 0x12, 0x34, 0x12, 0x34, 0x12, 0x78, 0x56, 0x34, 0x12, 0x78, 0x56}
#define BT_CHAR_CMD_UUID_128        {0x21, 0x43, 0x65, 0x87, 0xa9, 0xcb, 0x21, 0x43, 0x21, 0x43, 0x21, 0x43, 0x65, 0x87, 0xa9, 0xcb}
#define BT_CHAR_DATA_UUID_128       {0x55, 0x55, 0x55, 0x55, 0x44, 0x44, 0x33, 0x33, 0x22, 0x22, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11}

/* 蓝牙连接状态 */
typedef enum {
    BT_STATE_IDLE = 0,
    BT_STATE_SCANNING,
    BT_STATE_CONNECTING,
    BT_STATE_CONNECTED,
    BT_STATE_DISCONNECTED,
    BT_STATE_ERROR
} bt_connection_state_t;

/* 蓝牙事件类型 */
typedef enum {
    BT_EVENT_SCAN_START = 0,
    BT_EVENT_SCAN_RESULT,
    BT_EVENT_SCAN_COMPLETE,
    BT_EVENT_CONNECT_START,
    BT_EVENT_CONNECTED,
    BT_EVENT_DISCONNECTED,
    BT_EVENT_DATA_RECEIVED,
    BT_EVENT_DATA_SENT,
    BT_EVENT_ERROR
} bt_event_type_t;

/* 蓝牙数据包类型 */
typedef enum {
    BT_DATA_TYPE_CMD = 0x01,        // 命令数据
    BT_DATA_TYPE_RESPONSE = 0x02,   // 响应数据
    BT_DATA_TYPE_NOTIFICATION = 0x03 // 通知数据
} bt_data_type_t;

/* 蓝牙数据包结构 */
typedef struct {
    uint8_t type;                   // 数据类型
    uint8_t cmd;                    // 命令码
    uint16_t length;                // 数据长度
    uint8_t data[BT_MAX_DATA_LENGTH]; // 数据内容
    uint32_t timestamp;             // 时间戳
} bt_data_packet_t;

/* 蓝牙设备信息 */
typedef struct {
    esp_bd_addr_t addr;             // 设备地址
    char name[32];                  // 设备名称
    int8_t rssi;                    // 信号强度
    bool is_target;                 // 是否为目标设备
} bt_device_info_t;

/* 蓝牙连接信息 */
typedef struct {
    bt_connection_state_t state;    // 连接状态
    bt_device_info_t device;        // 设备信息
    uint16_t conn_id;               // 连接ID
    esp_gatt_if_t gattc_if;         // GATT客户端接口
    uint16_t service_handle;        // 服务句柄
    uint16_t char_cmd_handle;       // 命令特征值句柄
    uint16_t char_data_handle;      // 数据特征值句柄
    uint32_t connect_time;          // 连接时间
} bt_connection_info_t;

/* 蓝牙事件回调函数类型 */
typedef void (*bt_event_callback_t)(bt_event_type_t event, void *data);

/* 蓝牙数据接收回调函数类型 */
typedef void (*bt_data_callback_t)(const bt_data_packet_t *packet);

/**
 * @brief 初始化蓝牙系统
 * @return esp_err_t 
 */
esp_err_t bluetooth_system_init(void);

/**
 * @brief 启动蓝牙系统
 * @return esp_err_t 
 */
esp_err_t bluetooth_system_start(void);

/**
 * @brief 停止蓝牙系统
 * @return esp_err_t 
 */
esp_err_t bluetooth_system_stop(void);

/**
 * @brief 反初始化蓝牙系统
 * @return esp_err_t 
 */
esp_err_t bluetooth_system_deinit(void);

/**
 * @brief 开始扫描底座设备
 * @param timeout_s 扫描超时时间(秒)
 * @return esp_err_t 
 */
esp_err_t bluetooth_system_start_scan(uint32_t timeout_s);

/**
 * @brief 停止扫描
 * @return esp_err_t 
 */
esp_err_t bluetooth_system_stop_scan(void);

/**
 * @brief 连接到底座设备
 * @param device_addr 设备地址
 * @return esp_err_t 
 */
esp_err_t bluetooth_system_connect(const esp_bd_addr_t device_addr);

/**
 * @brief 断开连接
 * @return esp_err_t 
 */
esp_err_t bluetooth_system_disconnect(void);

/**
 * @brief 发送数据到底座
 * @param type 数据类型
 * @param cmd 命令码
 * @param data 数据内容
 * @param length 数据长度
 * @return esp_err_t 
 */
esp_err_t bluetooth_system_send_data(bt_data_type_t type, uint8_t cmd, 
                                    const uint8_t *data, uint16_t length);

/**
 * @brief 发送命令到底座
 * @param cmd 命令码
 * @param data 命令数据
 * @param length 数据长度
 * @return esp_err_t 
 */
esp_err_t bluetooth_system_send_command(uint8_t cmd, const uint8_t *data, uint16_t length);

/**
 * @brief 获取连接状态
 * @return bt_connection_state_t 
 */
bt_connection_state_t bluetooth_system_get_state(void);

/**
 * @brief 获取连接信息
 * @return const bt_connection_info_t* 
 */
const bt_connection_info_t* bluetooth_system_get_connection_info(void);

/**
 * @brief 检查是否已连接
 * @return true 已连接
 * @return false 未连接
 */
bool bluetooth_system_is_connected(void);

/**
 * @brief 注册事件回调函数
 * @param callback 回调函数
 * @return esp_err_t 
 */
esp_err_t bluetooth_system_register_event_callback(bt_event_callback_t callback);

/**
 * @brief 注册数据接收回调函数
 * @param callback 回调函数
 * @return esp_err_t 
 */
esp_err_t bluetooth_system_register_data_callback(bt_data_callback_t callback);

/**
 * @brief 获取扫描到的设备列表
 * @param devices 设备列表缓冲区
 * @param max_count 最大设备数量
 * @return uint8_t 实际设备数量
 */
uint8_t bluetooth_system_get_scan_results(bt_device_info_t *devices, uint8_t max_count);

#ifdef __cplusplus
}
#endif

#endif /* BLUETOOTH_SYSTEM_H */
