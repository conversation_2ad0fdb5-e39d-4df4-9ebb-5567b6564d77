/**
 * @file lvgl_port.h
 * @brief LVGL移植层头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef LVGL_PORT_H
#define LVGL_PORT_H

#include "esp_err.h"
#include "lvgl.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化LVGL移植层
 * @return esp_err_t 
 */
esp_err_t lvgl_port_init(void);

/**
 * @brief 反初始化LVGL移植层
 * @return esp_err_t 
 */
esp_err_t lvgl_port_deinit(void);

/**
 * @brief 获取显示器对象
 * @return lv_disp_t* 
 */
lv_disp_t* lvgl_port_get_display(void);

/**
 * @brief 获取触摸输入设备对象
 * @return lv_indev_t* 
 */
lv_indev_t* lvgl_port_get_touch_input(void);

#ifdef __cplusplus
}
#endif

#endif /* LVGL_PORT_H */
