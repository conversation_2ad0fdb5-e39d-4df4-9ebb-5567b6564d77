/**
 * @file event_manager.h
 * @brief TIMO事件管理器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef EVENT_MANAGER_H
#define EVENT_MANAGER_H

#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 事件类型定义 */
typedef enum {
    EVENT_TYPE_SYSTEM = 0,          // 系统事件
    EVENT_TYPE_UI,                  // UI事件
    EVENT_TYPE_SENSOR,              // 传感器事件
    EVENT_TYPE_AUDIO,               // 音频事件
    EVENT_TYPE_NETWORK,             // 网络事件
    EVENT_TYPE_ALARM,               // 闹钟事件
    EVENT_TYPE_TASK,                // 任务事件
    EVENT_TYPE_BLUETOOTH,           // 蓝牙事件
    EVENT_TYPE_STORAGE,             // 存储事件
    EVENT_TYPE_POWER,               // 电源事件
    EVENT_TYPE_MAX
} event_type_t;

/* 事件结构体 */
typedef struct {
    event_type_t type;              // 事件类型
    uint32_t id;                    // 事件ID
    void *data;                     // 事件数据
    size_t data_size;               // 数据大小
    uint64_t timestamp;             // 时间戳
} event_t;

/* 事件处理回调函数类型 */
typedef void (*event_handler_t)(const event_t *event);

/**
 * @brief 初始化事件管理器
 * @return esp_err_t 
 */
esp_err_t event_manager_init(void);

/**
 * @brief 启动事件管理器
 * @return esp_err_t 
 */
esp_err_t event_manager_start(void);

/**
 * @brief 停止事件管理器
 * @return esp_err_t 
 */
esp_err_t event_manager_stop(void);

/**
 * @brief 注册事件处理器
 * @param type 事件类型
 * @param handler 处理函数
 * @return esp_err_t 
 */
esp_err_t event_manager_register_handler(event_type_t type, event_handler_t handler);

/**
 * @brief 注销事件处理器
 * @param type 事件类型
 * @return esp_err_t 
 */
esp_err_t event_manager_unregister_handler(event_type_t type);

/**
 * @brief 发送事件
 * @param type 事件类型
 * @param id 事件ID
 * @param data 事件数据
 * @return esp_err_t 
 */
esp_err_t event_manager_post_event(event_type_t type, uint32_t id, void *data);

/**
 * @brief 发送事件（带数据大小）
 * @param type 事件类型
 * @param id 事件ID
 * @param data 事件数据
 * @param data_size 数据大小
 * @return esp_err_t 
 */
esp_err_t event_manager_post_event_with_size(event_type_t type, uint32_t id, void *data, size_t data_size);

/**
 * @brief 发送高优先级事件
 * @param type 事件类型
 * @param id 事件ID
 * @param data 事件数据
 * @return esp_err_t 
 */
esp_err_t event_manager_post_urgent_event(event_type_t type, uint32_t id, void *data);

/**
 * @brief 获取事件队列长度
 * @return uint32_t 队列中的事件数量
 */
uint32_t event_manager_get_queue_length(void);

/**
 * @brief 清空事件队列
 * @return esp_err_t 
 */
esp_err_t event_manager_clear_queue(void);

#ifdef __cplusplus
}
#endif

#endif /* EVENT_MANAGER_H */
