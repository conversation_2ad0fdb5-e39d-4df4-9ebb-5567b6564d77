idf_component_register(
    SRCS 
        "hardware_init.c"
        "i2c_bus.c"
        "spi_bus.c"
        "gpio_expander.c"
        "lcd_driver.c"
        "touch_driver.c"
        "sensor_drivers.c"
        "audio_driver.c"
        "sd_card.c"
        "rtc_driver.c"
        "battery_monitor.c"
    INCLUDE_DIRS 
        "include"
    REQUIRES 
        driver
        esp_lcd
        fatfs
        wear_levelling
        esp_timer
        esp_adc
        esp_codec_dev
        esp_audio_front_end
        log
        nvs_flash
)
