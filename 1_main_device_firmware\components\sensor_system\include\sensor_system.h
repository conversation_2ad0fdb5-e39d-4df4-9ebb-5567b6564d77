/**
 * @file sensor_system.h
 * @brief TIMO传感器系统头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef SENSOR_SYSTEM_H
#define SENSOR_SYSTEM_H

#include "esp_err.h"
#include "hardware_hal.h"
#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 传感器系统配置 */
typedef struct {
    uint32_t sample_interval_ms;    // 采样间隔(毫秒)
    bool auto_calibration;          // 自动校准
    bool data_logging;              // 数据记录
    uint8_t alert_threshold_temp;   // 温度警报阈值
    uint8_t alert_threshold_humi;   // 湿度警报阈值
    uint16_t alert_threshold_co2;   // CO2警报阈值
    uint16_t alert_threshold_lux;   // 光照警报阈值
} sensor_config_t;

/* 环境数据结构体 */
typedef struct {
    float temperature;              // 温度 (°C)
    float humidity;                 // 湿度 (%)
    float lux;                      // 光照强度 (lux)
    uint16_t co2;                   // CO2浓度 (ppm)
    uint16_t tvoc;                  // TVOC浓度 (ppb)
    float acc_x, acc_y, acc_z;      // 加速度 (g)
    float gyro_x, gyro_y, gyro_z;   // 角速度 (dps)
    uint64_t timestamp;             // 时间戳
    bool valid;                     // 数据有效性
} environment_data_t;

/* 环境状态评估 */
typedef enum {
    ENV_STATUS_EXCELLENT = 0,       // 优秀
    ENV_STATUS_GOOD,                // 良好
    ENV_STATUS_MODERATE,            // 一般
    ENV_STATUS_POOR,                // 较差
    ENV_STATUS_UNHEALTHY,           // 不健康
    ENV_STATUS_MAX
} environment_status_t;

/* 传感器警报类型 */
typedef enum {
    SENSOR_ALERT_NONE = 0,          // 无警报
    SENSOR_ALERT_TEMP_HIGH,         // 温度过高
    SENSOR_ALERT_TEMP_LOW,          // 温度过低
    SENSOR_ALERT_HUMIDITY_HIGH,     // 湿度过高
    SENSOR_ALERT_HUMIDITY_LOW,      // 湿度过低
    SENSOR_ALERT_CO2_HIGH,          // CO2浓度过高
    SENSOR_ALERT_LIGHT_LOW,         // 光照不足
    SENSOR_ALERT_LIGHT_HIGH,        // 光照过强
    SENSOR_ALERT_AIR_QUALITY,       // 空气质量差
    SENSOR_ALERT_MAX
} sensor_alert_t;

/* 传感器事件回调函数类型 */
typedef void (*sensor_event_callback_t)(environment_data_t *data, sensor_alert_t alert);

/**
 * @brief 初始化传感器系统
 * @return esp_err_t 
 */
esp_err_t sensor_system_init(void);

/**
 * @brief 启动传感器系统
 * @return esp_err_t 
 */
esp_err_t sensor_system_start(void);

/**
 * @brief 停止传感器系统
 * @return esp_err_t 
 */
esp_err_t sensor_system_stop(void);

/**
 * @brief 获取最新环境数据
 * @return environment_data_t* 
 */
environment_data_t* sensor_system_get_latest_data(void);

/**
 * @brief 获取环境状态评估
 * @return environment_status_t 
 */
environment_status_t sensor_system_get_environment_status(void);

/**
 * @brief 设置传感器配置
 * @param config 配置结构体
 * @return esp_err_t 
 */
esp_err_t sensor_system_set_config(const sensor_config_t *config);

/**
 * @brief 获取传感器配置
 * @return sensor_config_t* 
 */
sensor_config_t* sensor_system_get_config(void);

/**
 * @brief 注册传感器事件回调
 * @param callback 回调函数
 * @return esp_err_t 
 */
esp_err_t sensor_system_register_callback(sensor_event_callback_t callback);

/**
 * @brief 手动触发传感器读取
 * @return esp_err_t 
 */
esp_err_t sensor_system_trigger_read(void);

/**
 * @brief 校准传感器
 * @return esp_err_t 
 */
esp_err_t sensor_system_calibrate(void);

/**
 * @brief 获取传感器状态
 * @param sensor_name 传感器名称
 * @return bool true=正常, false=异常
 */
bool sensor_system_get_sensor_status(const char *sensor_name);

/**
 * @brief 获取数据历史记录
 * @param data 数据数组
 * @param count 数组大小，返回实际数量
 * @param hours 历史小时数
 * @return esp_err_t 
 */
esp_err_t sensor_system_get_history(environment_data_t *data, uint16_t *count, uint8_t hours);

/**
 * @brief 清除历史数据
 * @return esp_err_t 
 */
esp_err_t sensor_system_clear_history(void);

/**
 * @brief 导出数据为JSON格式
 * @param buffer 输出缓冲区
 * @param size 缓冲区大小
 * @return esp_err_t 
 */
esp_err_t sensor_system_export_json(char *buffer, size_t size);

/**
 * @brief 获取环境舒适度评分
 * @return uint8_t 评分 (0-100)
 */
uint8_t sensor_system_get_comfort_score(void);

/**
 * @brief 获取空气质量指数
 * @return uint16_t AQI值
 */
uint16_t sensor_system_get_air_quality_index(void);

/**
 * @brief 检查是否需要开窗通风
 * @return bool true=需要, false=不需要
 */
bool sensor_system_need_ventilation(void);

/**
 * @brief 获取建议信息
 * @param buffer 输出缓冲区
 * @param size 缓冲区大小
 * @return esp_err_t 
 */
esp_err_t sensor_system_get_suggestions(char *buffer, size_t size);

#ifdef __cplusplus
}
#endif

#endif /* SENSOR_SYSTEM_H */
