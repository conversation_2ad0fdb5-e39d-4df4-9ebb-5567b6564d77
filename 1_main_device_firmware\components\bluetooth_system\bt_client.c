/**
 * @file bt_client.c
 * @brief TIMO蓝牙客户端实现
 * @version 1.0.0
 * @date 2025-06-28
 */

#include "bt_client.h"
#include "esp_log.h"
#include "esp_bt.h"
#include "esp_bt_main.h"
#include "esp_gap_ble_api.h"
#include "esp_gattc_api.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include <string.h>

static const char *TAG = "BT_CLIENT";

/* 客户端状态 */
static bool g_client_initialized = false;
static bt_client_state_t g_client_state = BT_CLIENT_STATE_IDLE;
static bt_connection_info_t g_conn_info = {0};
static bt_service_discovery_t g_service_info = {0};

/* 回调函数 */
static bt_client_event_cb_t g_event_callback = NULL;
static bt_client_data_cb_t g_data_callback = NULL;

/* 扫描相关 */
static bt_device_info_t g_scan_devices[10];
static uint8_t g_scan_device_count = 0;
static uint32_t g_scan_timeout_ms = 0;
static esp_timer_handle_t g_scan_timer = NULL;

/* 互斥锁 */
static SemaphoreHandle_t g_client_mutex = NULL;

/* UUID定义 */
static uint8_t g_service_uuid[16] = BT_SERVICE_UUID_128;
static uint8_t g_char_cmd_uuid[16] = BT_CHAR_CMD_UUID_128;
static uint8_t g_char_data_uuid[16] = BT_CHAR_DATA_UUID_128;

/**
 * @brief 发送客户端事件
 */
static void send_client_event(bt_event_type_t event, void *data)
{
    if (g_event_callback) {
        g_event_callback(event, data);
    }
}

/**
 * @brief 扫描超时回调
 */
static void scan_timeout_callback(void *arg)
{
    ESP_LOGI(TAG, "扫描超时");
    esp_ble_gap_stop_scanning();
}

/**
 * @brief 比较UUID
 */
static bool uuid_compare(const uint8_t *uuid1, const uint8_t *uuid2, uint8_t len)
{
    return memcmp(uuid1, uuid2, len) == 0;
}

/**
 * @brief GAP事件处理
 */
static void gap_event_handler(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t *param)
{
    switch (event) {
        case ESP_GAP_BLE_SCAN_PARAM_SET_COMPLETE_EVT:
            ESP_LOGI(TAG, "扫描参数设置完成");
            break;
            
        case ESP_GAP_BLE_SCAN_START_COMPLETE_EVT:
            if (param->scan_start_cmpl.status == ESP_BT_STATUS_SUCCESS) {
                ESP_LOGI(TAG, "开始扫描");
                g_client_state = BT_CLIENT_STATE_SCANNING;
                send_client_event(BT_EVENT_SCAN_START, NULL);
            } else {
                ESP_LOGE(TAG, "扫描启动失败");
                g_client_state = BT_CLIENT_STATE_ERROR;
                send_client_event(BT_EVENT_ERROR, NULL);
            }
            break;
            
        case ESP_GAP_BLE_SCAN_RESULT_EVT:
            {
                esp_ble_gap_cb_param_t *scan_result = param;
                switch (scan_result->scan_rst.search_evt) {
                    case ESP_GAP_SEARCH_INQ_RES_EVT:
                        {
                            // 处理扫描结果
                            bt_device_info_t device = {0};
                            memcpy(device.addr, scan_result->scan_rst.bda, sizeof(esp_bd_addr_t));
                            device.rssi = scan_result->scan_rst.rssi;
                            device.is_target = false;
                            
                            // 解析设备名称
                            uint8_t *adv_name = NULL;
                            uint8_t adv_name_len = 0;
                            adv_name = esp_ble_resolve_adv_data(scan_result->scan_rst.ble_adv,
                                                               ESP_BLE_AD_TYPE_NAME_CMPL, &adv_name_len);
                            if (adv_name) {
                                memcpy(device.name, adv_name, 
                                       (adv_name_len < sizeof(device.name)-1) ? adv_name_len : sizeof(device.name)-1);
                                device.name[adv_name_len] = '\0';
                            }
                            
                            // 检查是否为目标设备
                            if (strstr(device.name, BT_BASE_DEVICE_NAME) != NULL) {
                                device.is_target = true;
                                ESP_LOGI(TAG, "发现目标设备: %s, RSSI: %d", device.name, device.rssi);
                            }
                            
                            // 添加到扫描结果
                            xSemaphoreTake(g_client_mutex, portMAX_DELAY);
                            if (g_scan_device_count < sizeof(g_scan_devices)/sizeof(g_scan_devices[0])) {
                                memcpy(&g_scan_devices[g_scan_device_count], &device, sizeof(bt_device_info_t));
                                g_scan_device_count++;
                            }
                            xSemaphoreGive(g_client_mutex);
                            
                            send_client_event(BT_EVENT_SCAN_RESULT, &device);
                        }
                        break;
                        
                    case ESP_GAP_SEARCH_INQ_CMPL_EVT:
                        ESP_LOGI(TAG, "扫描完成");
                        g_client_state = BT_CLIENT_STATE_SCAN_COMPLETE;
                        
                        // 停止扫描定时器
                        if (g_scan_timer) {
                            esp_timer_stop(g_scan_timer);
                        }
                        
                        send_client_event(BT_EVENT_SCAN_COMPLETE, NULL);
                        break;
                        
                    default:
                        break;
                }
            }
            break;
            
        case ESP_GAP_BLE_SCAN_STOP_COMPLETE_EVT:
            if (param->scan_stop_cmpl.status == ESP_BT_STATUS_SUCCESS) {
                ESP_LOGI(TAG, "扫描停止");
                if (g_client_state == BT_CLIENT_STATE_SCANNING) {
                    g_client_state = BT_CLIENT_STATE_SCAN_COMPLETE;
                }
            }
            break;
            
        case ESP_GAP_BLE_UPDATE_CONN_PARAMS_EVT:
            ESP_LOGI(TAG, "连接参数更新: 间隔=%d, 延迟=%d, 超时=%d",
                     param->update_conn_params.conn_int,
                     param->update_conn_params.latency,
                     param->update_conn_params.timeout);
            break;
            
        default:
            break;
    }
}

/**
 * @brief GATTC事件处理
 */
static void gattc_event_handler(esp_gattc_cb_event_t event, esp_gatt_if_t gattc_if, 
                               esp_ble_gattc_cb_param_t *param)
{
    switch (event) {
        case ESP_GATTC_REG_EVT:
            ESP_LOGI(TAG, "GATTC注册完成");
            g_conn_info.gattc_if = gattc_if;
            break;
            
        case ESP_GATTC_CONNECT_EVT:
            ESP_LOGI(TAG, "GATTC连接成功");
            g_client_state = BT_CLIENT_STATE_CONNECTED;
            g_conn_info.conn_id = param->connect.conn_id;
            memcpy(g_conn_info.device.addr, param->connect.remote_bda, sizeof(esp_bd_addr_t));
            g_conn_info.connect_time = esp_timer_get_time() / 1000;
            
            send_client_event(BT_EVENT_CONNECTED, &g_conn_info);
            
            // 开始服务发现
            esp_ble_gattc_search_service(gattc_if, param->connect.conn_id, NULL);
            g_client_state = BT_CLIENT_STATE_DISCOVERING;
            break;
            
        case ESP_GATTC_DISCONNECT_EVT:
            ESP_LOGI(TAG, "GATTC断开连接");
            g_client_state = BT_CLIENT_STATE_DISCONNECTED;
            memset(&g_conn_info, 0, sizeof(bt_connection_info_t));
            memset(&g_service_info, 0, sizeof(bt_service_discovery_t));
            
            send_client_event(BT_EVENT_DISCONNECTED, NULL);
            break;
            
        case ESP_GATTC_SEARCH_RES_EVT:
            {
                // 检查是否为目标服务
                esp_bt_uuid_t service_uuid;
                service_uuid.len = ESP_UUID_LEN_128;
                memcpy(service_uuid.uuid.uuid128, g_service_uuid, 16);
                
                if (param->search_res.srvc_id.uuid.len == ESP_UUID_LEN_128 &&
                    uuid_compare(param->search_res.srvc_id.uuid.uuid.uuid128, g_service_uuid, 16)) {
                    ESP_LOGI(TAG, "发现目标服务");
                    g_service_info.service_found = true;
                    g_service_info.service_start_handle = param->search_res.start_handle;
                    g_service_info.service_end_handle = param->search_res.end_handle;
                }
            }
            break;
            
        case ESP_GATTC_SEARCH_CMPL_EVT:
            ESP_LOGI(TAG, "服务发现完成");
            if (g_service_info.service_found) {
                // 获取特征值
                esp_ble_gattc_get_all_char(gattc_if, param->search_cmpl.conn_id,
                                          g_service_info.service_start_handle,
                                          g_service_info.service_end_handle);
            } else {
                ESP_LOGE(TAG, "未发现目标服务");
                g_client_state = BT_CLIENT_STATE_ERROR;
                send_client_event(BT_EVENT_ERROR, NULL);
            }
            break;
            
        case ESP_GATTC_GET_CHAR_EVT:
            {
                // 检查特征值UUID
                if (param->get_char.status == ESP_GATT_OK) {
                    if (param->get_char.char_id.uuid.len == ESP_UUID_LEN_128) {
                        if (uuid_compare(param->get_char.char_id.uuid.uuid.uuid128, g_char_cmd_uuid, 16)) {
                            ESP_LOGI(TAG, "发现命令特征值");
                            g_service_info.char_cmd_found = true;
                            g_service_info.char_cmd_handle = param->get_char.char_handle;
                        } else if (uuid_compare(param->get_char.char_id.uuid.uuid.uuid128, g_char_data_uuid, 16)) {
                            ESP_LOGI(TAG, "发现数据特征值");
                            g_service_info.char_data_found = true;
                            g_service_info.char_data_handle = param->get_char.char_handle;
                        }
                    }
                }
            }
            break;
            
        case ESP_GATTC_REG_FOR_NOTIFY_EVT:
            ESP_LOGI(TAG, "通知注册完成");
            if (g_service_info.char_cmd_found && g_service_info.char_data_found) {
                g_client_state = BT_CLIENT_STATE_READY;
                ESP_LOGI(TAG, "客户端准备就绪");
            }
            break;
            
        case ESP_GATTC_NOTIFY_EVT:
            ESP_LOGI(TAG, "收到通知数据: %d bytes", param->notify.value_len);
            if (g_data_callback) {
                g_data_callback(param->notify.value, param->notify.value_len);
            }
            break;
            
        case ESP_GATTC_WRITE_EVT:
            if (param->write.status == ESP_GATT_OK) {
                ESP_LOGD(TAG, "写入成功");
            } else {
                ESP_LOGE(TAG, "写入失败: %d", param->write.status);
            }
            break;
            
        case ESP_GATTC_READ_EVT:
            if (param->read.status == ESP_GATT_OK) {
                ESP_LOGI(TAG, "读取成功: %d bytes", param->read.value_len);
                if (g_data_callback) {
                    g_data_callback(param->read.value, param->read.value_len);
                }
            } else {
                ESP_LOGE(TAG, "读取失败: %d", param->read.status);
            }
            break;
            
        default:
            break;
    }
}

/**
 * @brief 初始化BLE客户端
 */
esp_err_t bt_client_init(void)
{
    if (g_client_initialized) {
        ESP_LOGW(TAG, "BLE客户端已初始化");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "初始化BLE客户端...");

    // 创建互斥锁
    g_client_mutex = xSemaphoreCreateMutex();
    if (!g_client_mutex) {
        ESP_LOGE(TAG, "创建互斥锁失败");
        return ESP_ERR_NO_MEM;
    }

    // 初始化蓝牙控制器
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    esp_err_t ret = esp_bt_controller_init(&bt_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "蓝牙控制器初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "蓝牙控制器启用失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 初始化蓝牙栈
    ret = esp_bluedroid_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Bluedroid初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = esp_bluedroid_enable();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Bluedroid启用失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 注册GAP回调
    ret = esp_ble_gap_register_callback(gap_event_handler);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GAP回调注册失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 注册GATTC回调
    ret = esp_ble_gattc_register_callback(gattc_event_handler);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GATTC回调注册失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 注册GATTC应用
    ret = esp_ble_gattc_app_register(BT_CLIENT_APP_ID);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GATTC应用注册失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 设置扫描参数
    esp_ble_scan_params_t scan_params = {
        .scan_type = BT_SCAN_TYPE,
        .own_addr_type = BLE_ADDR_TYPE_PUBLIC,
        .scan_filter_policy = BT_SCAN_FILTER_POLICY,
        .scan_interval = BT_SCAN_INTERVAL,
        .scan_window = BT_SCAN_WINDOW,
        .scan_duplicate = BLE_SCAN_DUPLICATE_DISABLE
    };

    ret = esp_ble_gap_set_scan_params(&scan_params);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置扫描参数失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 创建扫描定时器
    esp_timer_create_args_t timer_args = {
        .callback = scan_timeout_callback,
        .arg = NULL,
        .name = "scan_timer"
    };
    ret = esp_timer_create(&timer_args, &g_scan_timer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建扫描定时器失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 初始化状态
    g_client_state = BT_CLIENT_STATE_IDLE;
    memset(&g_conn_info, 0, sizeof(bt_connection_info_t));
    memset(&g_service_info, 0, sizeof(bt_service_discovery_t));
    g_scan_device_count = 0;

    g_client_initialized = true;
    ESP_LOGI(TAG, "BLE客户端初始化完成");

    return ESP_OK;
}

/**
 * @brief 反初始化BLE客户端
 */
esp_err_t bt_client_deinit(void)
{
    if (!g_client_initialized) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "反初始化BLE客户端...");

    // 断开连接
    if (g_client_state == BT_CLIENT_STATE_CONNECTED ||
        g_client_state == BT_CLIENT_STATE_READY) {
        bt_client_disconnect();
    }

    // 停止扫描
    if (g_client_state == BT_CLIENT_STATE_SCANNING) {
        bt_client_stop_scan();
    }

    // 删除定时器
    if (g_scan_timer) {
        esp_timer_delete(g_scan_timer);
        g_scan_timer = NULL;
    }

    // 反初始化蓝牙栈
    esp_bluedroid_disable();
    esp_bluedroid_deinit();
    esp_bt_controller_disable();
    esp_bt_controller_deinit();

    // 清理资源
    if (g_client_mutex) {
        vSemaphoreDelete(g_client_mutex);
        g_client_mutex = NULL;
    }

    g_client_initialized = false;
    ESP_LOGI(TAG, "BLE客户端反初始化完成");

    return ESP_OK;
}

/**
 * @brief 开始扫描设备
 */
esp_err_t bt_client_start_scan(uint32_t timeout_s)
{
    if (!g_client_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (g_client_state == BT_CLIENT_STATE_SCANNING) {
        ESP_LOGW(TAG, "正在扫描中");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "开始扫描设备，超时: %d 秒", timeout_s);

    // 清除扫描结果
    xSemaphoreTake(g_client_mutex, portMAX_DELAY);
    g_scan_device_count = 0;
    memset(g_scan_devices, 0, sizeof(g_scan_devices));
    xSemaphoreGive(g_client_mutex);

    // 设置扫描超时
    if (timeout_s > 0) {
        g_scan_timeout_ms = timeout_s * 1000;
        esp_timer_start_once(g_scan_timer, g_scan_timeout_ms * 1000);
    }

    // 开始扫描
    esp_err_t ret = esp_ble_gap_start_scanning(timeout_s);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "启动扫描失败: %s", esp_err_to_name(ret));
        if (g_scan_timer) {
            esp_timer_stop(g_scan_timer);
        }
    }

    return ret;
}

/**
 * @brief 停止扫描
 */
esp_err_t bt_client_stop_scan(void)
{
    if (g_client_state != BT_CLIENT_STATE_SCANNING) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "停止扫描");

    // 停止定时器
    if (g_scan_timer) {
        esp_timer_stop(g_scan_timer);
    }

    return esp_ble_gap_stop_scanning();
}

/**
 * @brief 连接到指定设备
 */
esp_err_t bt_client_connect(const esp_bd_addr_t remote_addr)
{
    if (!g_client_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (g_client_state == BT_CLIENT_STATE_CONNECTED ||
        g_client_state == BT_CLIENT_STATE_READY) {
        ESP_LOGW(TAG, "已连接到设备");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "连接到设备");

    // 停止扫描
    if (g_client_state == BT_CLIENT_STATE_SCANNING) {
        bt_client_stop_scan();
    }

    g_client_state = BT_CLIENT_STATE_CONNECTING;
    send_client_event(BT_EVENT_CONNECT_START, NULL);

    return esp_ble_gattc_open(g_conn_info.gattc_if, remote_addr, BLE_ADDR_TYPE_PUBLIC, true);
}

/**
 * @brief 断开连接
 */
esp_err_t bt_client_disconnect(void)
{
    if (g_client_state != BT_CLIENT_STATE_CONNECTED &&
        g_client_state != BT_CLIENT_STATE_READY) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "断开连接");

    return esp_ble_gattc_close(g_conn_info.gattc_if, g_conn_info.conn_id);
}

/**
 * @brief 发现服务
 */
esp_err_t bt_client_discover_services(void)
{
    if (g_client_state != BT_CLIENT_STATE_CONNECTED) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "发现服务");
    g_client_state = BT_CLIENT_STATE_DISCOVERING;

    return esp_ble_gattc_search_service(g_conn_info.gattc_if, g_conn_info.conn_id, NULL);
}

/**
 * @brief 写入特征值
 */
esp_err_t bt_client_write_char(uint16_t char_handle, const uint8_t *data, uint16_t length)
{
    if (g_client_state != BT_CLIENT_STATE_READY) {
        return ESP_ERR_INVALID_STATE;
    }

    if (!data || length == 0) {
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGD(TAG, "写入特征值: handle=0x%04X, len=%d", char_handle, length);

    return esp_ble_gattc_write_char(g_conn_info.gattc_if, g_conn_info.conn_id,
                                   char_handle, length, (uint8_t*)data,
                                   ESP_GATT_WRITE_TYPE_RSP, ESP_GATT_AUTH_REQ_NONE);
}

/**
 * @brief 读取特征值
 */
esp_err_t bt_client_read_char(uint16_t char_handle)
{
    if (g_client_state != BT_CLIENT_STATE_READY) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGD(TAG, "读取特征值: handle=0x%04X", char_handle);

    return esp_ble_gattc_read_char(g_conn_info.gattc_if, g_conn_info.conn_id,
                                  char_handle, ESP_GATT_AUTH_REQ_NONE);
}

/**
 * @brief 启用特征值通知
 */
esp_err_t bt_client_enable_notification(uint16_t char_handle, bool enable)
{
    if (g_client_state != BT_CLIENT_STATE_CONNECTED &&
        g_client_state != BT_CLIENT_STATE_READY) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "%s特征值通知: handle=0x%04X", enable ? "启用" : "禁用", char_handle);

    return esp_ble_gattc_register_for_notify(g_conn_info.gattc_if,
                                            g_conn_info.device.addr, char_handle);
}

/**
 * @brief 获取客户端状态
 */
bt_client_state_t bt_client_get_state(void)
{
    return g_client_state;
}

/**
 * @brief 获取连接信息
 */
const bt_connection_info_t* bt_client_get_connection_info(void)
{
    return &g_conn_info;
}

/**
 * @brief 获取服务发现信息
 */
const bt_service_discovery_t* bt_client_get_service_info(void)
{
    return &g_service_info;
}

/**
 * @brief 检查是否已连接并准备就绪
 */
bool bt_client_is_ready(void)
{
    return (g_client_state == BT_CLIENT_STATE_READY) &&
           g_service_info.service_found &&
           g_service_info.char_cmd_found &&
           g_service_info.char_data_found;
}

/**
 * @brief 注册事件回调函数
 */
esp_err_t bt_client_register_event_callback(bt_client_event_cb_t callback)
{
    g_event_callback = callback;
    return ESP_OK;
}

/**
 * @brief 注册数据接收回调函数
 */
esp_err_t bt_client_register_data_callback(bt_client_data_cb_t callback)
{
    g_data_callback = callback;
    return ESP_OK;
}

/**
 * @brief 获取扫描结果
 */
uint8_t bt_client_get_scan_results(bt_device_info_t *devices, uint8_t max_count)
{
    if (!devices || max_count == 0) {
        return 0;
    }

    xSemaphoreTake(g_client_mutex, portMAX_DELAY);

    uint8_t count = (g_scan_device_count < max_count) ? g_scan_device_count : max_count;
    memcpy(devices, g_scan_devices, count * sizeof(bt_device_info_t));

    xSemaphoreGive(g_client_mutex);

    return count;
}

/**
 * @brief 清除扫描结果
 */
void bt_client_clear_scan_results(void)
{
    xSemaphoreTake(g_client_mutex, portMAX_DELAY);
    g_scan_device_count = 0;
    memset(g_scan_devices, 0, sizeof(g_scan_devices));
    xSemaphoreGive(g_client_mutex);
}

/**
 * @brief 设置扫描参数
 */
esp_err_t bt_client_set_scan_params(uint16_t interval, uint16_t window)
{
    esp_ble_scan_params_t scan_params = {
        .scan_type = BT_SCAN_TYPE,
        .own_addr_type = BLE_ADDR_TYPE_PUBLIC,
        .scan_filter_policy = BT_SCAN_FILTER_POLICY,
        .scan_interval = interval,
        .scan_window = window,
        .scan_duplicate = BLE_SCAN_DUPLICATE_DISABLE
    };

    return esp_ble_gap_set_scan_params(&scan_params);
}

/**
 * @brief 设置连接参数
 */
esp_err_t bt_client_set_conn_params(uint16_t interval_min, uint16_t interval_max,
                                   uint16_t latency, uint16_t timeout)
{
    if (g_client_state != BT_CLIENT_STATE_CONNECTED &&
        g_client_state != BT_CLIENT_STATE_READY) {
        return ESP_ERR_INVALID_STATE;
    }

    esp_ble_conn_update_params_t conn_params = {
        .bda = {0},
        .min_int = interval_min,
        .max_int = interval_max,
        .latency = latency,
        .timeout = timeout
    };

    memcpy(conn_params.bda, g_conn_info.device.addr, sizeof(esp_bd_addr_t));

    return esp_ble_gap_update_conn_params(&conn_params);
}

/**
 * @brief 获取连接的RSSI值
 */
int8_t bt_client_get_rssi(void)
{
    return g_conn_info.device.rssi;
}

/**
 * @brief 更新连接参数
 */
esp_err_t bt_client_update_conn_params(void)
{
    return bt_client_set_conn_params(BT_CONN_INTERVAL_MIN, BT_CONN_INTERVAL_MAX,
                                    BT_CONN_LATENCY, BT_CONN_TIMEOUT);
}
