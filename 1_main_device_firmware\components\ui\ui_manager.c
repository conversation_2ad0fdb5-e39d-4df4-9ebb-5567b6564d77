/**
 * @file ui_manager.c
 * @brief TIMO用户界面管理器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "ui_manager.h"
#include "ui_theme.h"
#include "ui_watchface.h"
#include "ui_menu.h"
#include "ui_settings.h"
#include "ui_alarm.h"
#include "ui_sensor.h"
#include "lvgl_port.h"
#include "hardware_hal.h"
#include "system_manager.h"
#include "event_manager.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

static const char *TAG = "UI_MANAGER";

/* UI管理器状态 */
static bool g_ui_initialized = false;
static bool g_ui_running = false;
static ui_page_t g_current_page = UI_PAGE_WATCHFACE;
static ui_config_t g_ui_config;
static ui_event_callback_t g_event_callback = NULL;

/* LVGL相关 */
static lv_disp_t *g_display = NULL;
static lv_indev_t *g_touch_input = NULL;
static TaskHandle_t g_ui_task_handle = NULL;
static SemaphoreHandle_t g_ui_mutex = NULL;

/* 页面对象 */
static lv_obj_t *g_pages[UI_PAGE_MAX] = {NULL};
static lv_obj_t *g_current_screen = NULL;

/* 默认UI配置 */
static const ui_config_t DEFAULT_UI_CONFIG = {
    .brightness = 50,
    .theme_id = 0,
    .auto_brightness = true,
    .screen_timeout = 30000,  // 30秒
    .gesture_enabled = true
};

/**
 * @brief UI任务
 */
static void ui_task(void *pvParameters)
{
    ESP_LOGI(TAG, "UI任务启动");
    
    while (g_ui_running) {
        // 处理LVGL任务
        xSemaphoreTake(g_ui_mutex, portMAX_DELAY);
        lv_timer_handler();
        xSemaphoreGive(g_ui_mutex);
        
        // 延时5ms
        vTaskDelay(pdMS_TO_TICKS(5));
    }
    
    ESP_LOGI(TAG, "UI任务结束");
    vTaskDelete(NULL);
}

/**
 * @brief 触摸事件处理
 */
static void touch_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    
    if (code == LV_EVENT_CLICKED) {
        ESP_LOGD(TAG, "触摸点击事件");
        ui_manager_send_event(UI_EVENT_BUTTON_CLICKED, NULL);
    }
}

/**
 * @brief 创建页面
 */
static esp_err_t create_pages(void)
{
    ESP_LOGI(TAG, "创建UI页面...");
    
    // 创建表盘页面
    g_pages[UI_PAGE_WATCHFACE] = ui_watchface_create();
    if (!g_pages[UI_PAGE_WATCHFACE]) {
        ESP_LOGE(TAG, "创建表盘页面失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建主菜单页面
    g_pages[UI_PAGE_MENU] = ui_menu_create();
    if (!g_pages[UI_PAGE_MENU]) {
        ESP_LOGE(TAG, "创建主菜单页面失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建闹钟页面
    g_pages[UI_PAGE_ALARM] = ui_alarm_create();
    if (!g_pages[UI_PAGE_ALARM]) {
        ESP_LOGE(TAG, "创建闹钟页面失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建传感器页面
    g_pages[UI_PAGE_SENSOR] = ui_sensor_create();
    if (!g_pages[UI_PAGE_SENSOR]) {
        ESP_LOGE(TAG, "创建传感器页面失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建设置页面
    g_pages[UI_PAGE_SETTINGS] = ui_settings_create();
    if (!g_pages[UI_PAGE_SETTINGS]) {
        ESP_LOGE(TAG, "创建设置页面失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 设置默认页面
    g_current_screen = g_pages[UI_PAGE_WATCHFACE];
    lv_scr_load(g_current_screen);
    
    ESP_LOGI(TAG, "UI页面创建完成");
    return ESP_OK;
}

/**
 * @brief 初始化UI管理器
 */
esp_err_t ui_manager_init(void)
{
    if (g_ui_initialized) {
        ESP_LOGW(TAG, "UI管理器已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化UI管理器...");
    
    // 创建互斥锁
    g_ui_mutex = xSemaphoreCreateMutex();
    if (!g_ui_mutex) {
        ESP_LOGE(TAG, "创建UI互斥锁失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 初始化LVGL移植层
    esp_err_t ret = lvgl_port_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LVGL移植层初始化失败");
        return ret;
    }
    
    // 获取显示器和触摸输入设备
    g_display = lv_disp_get_default();
    g_touch_input = lv_indev_get_next(NULL);
    
    if (!g_display) {
        ESP_LOGE(TAG, "获取显示器失败");
        return ESP_ERR_INVALID_STATE;
    }
    
    // 初始化UI主题
    ret = ui_theme_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "UI主题初始化失败");
        return ret;
    }
    
    // 加载UI配置
    memcpy(&g_ui_config, &DEFAULT_UI_CONFIG, sizeof(ui_config_t));
    
    // 创建页面
    ret = create_pages();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建页面失败");
        return ret;
    }
    
    g_ui_initialized = true;
    ESP_LOGI(TAG, "UI管理器初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 启动UI管理器
 */
esp_err_t ui_manager_start(void)
{
    if (!g_ui_initialized) {
        ESP_LOGE(TAG, "UI管理器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (g_ui_running) {
        ESP_LOGW(TAG, "UI管理器已启动");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "启动UI管理器...");
    
    // 创建UI任务
    BaseType_t ret = xTaskCreate(
        ui_task,
        "ui_task",
        8192,
        NULL,
        6,  // 高优先级
        &g_ui_task_handle
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建UI任务失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 设置屏幕亮度
    ui_manager_set_brightness(g_ui_config.brightness);
    
    // 应用主题
    ui_theme_apply(g_ui_config.theme_id);
    
    g_ui_running = true;
    ESP_LOGI(TAG, "UI管理器启动完成");
    
    return ESP_OK;
}

/**
 * @brief 停止UI管理器
 */
esp_err_t ui_manager_stop(void)
{
    if (!g_ui_running) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "停止UI管理器...");
    
    g_ui_running = false;
    
    // 停止UI任务
    if (g_ui_task_handle) {
        vTaskDelete(g_ui_task_handle);
        g_ui_task_handle = NULL;
    }
    
    ESP_LOGI(TAG, "UI管理器停止完成");
    return ESP_OK;
}

/**
 * @brief 切换到指定页面
 */
esp_err_t ui_manager_switch_page(ui_page_t page)
{
    if (page >= UI_PAGE_MAX) {
        ESP_LOGE(TAG, "无效的页面ID: %d", page);
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_pages[page]) {
        ESP_LOGE(TAG, "页面未创建: %d", page);
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "切换页面: %d -> %d", g_current_page, page);
    
    xSemaphoreTake(g_ui_mutex, portMAX_DELAY);
    
    // 切换屏幕
    lv_scr_load_anim(g_pages[page], LV_SCR_LOAD_ANIM_FADE_IN, 300, 0, false);
    g_current_screen = g_pages[page];
    g_current_page = page;
    
    xSemaphoreGive(g_ui_mutex);
    
    // 发送页面切换事件
    ui_manager_send_event(UI_EVENT_PAGE_CHANGED, &page);
    
    return ESP_OK;
}

/**
 * @brief 获取当前页面
 */
ui_page_t ui_manager_get_current_page(void)
{
    return g_current_page;
}

/**
 * @brief 设置屏幕亮度
 */
esp_err_t ui_manager_set_brightness(uint8_t brightness)
{
    if (brightness > 100) {
        brightness = 100;
    }
    
    esp_err_t ret = lcd_set_backlight(brightness);
    if (ret == ESP_OK) {
        g_ui_config.brightness = brightness;
        ESP_LOGI(TAG, "设置屏幕亮度: %d%%", brightness);
    }
    
    return ret;
}

/**
 * @brief 获取屏幕亮度
 */
uint8_t ui_manager_get_brightness(void)
{
    return g_ui_config.brightness;
}

/**
 * @brief 设置主题
 */
esp_err_t ui_manager_set_theme(uint8_t theme_id)
{
    esp_err_t ret = ui_theme_apply(theme_id);
    if (ret == ESP_OK) {
        g_ui_config.theme_id = theme_id;
        ESP_LOGI(TAG, "设置主题: %d", theme_id);
    }
    
    return ret;
}

/**
 * @brief 获取当前主题
 */
uint8_t ui_manager_get_theme(void)
{
    return g_ui_config.theme_id;
}

/**
 * @brief 注册UI事件回调
 */
esp_err_t ui_manager_register_event_callback(ui_event_callback_t callback)
{
    g_event_callback = callback;
    return ESP_OK;
}

/**
 * @brief 发送UI事件
 */
esp_err_t ui_manager_send_event(ui_event_t event, void *data)
{
    if (g_event_callback) {
        g_event_callback(event, data);
    }
    
    return event_manager_post_event(EVENT_TYPE_UI, event, data);
}

/**
 * @brief 更新传感器数据显示
 */
esp_err_t ui_manager_update_sensor_data(float temperature, float humidity, float lux, uint16_t co2)
{
    if (g_current_page == UI_PAGE_SENSOR) {
        return ui_sensor_update_data(temperature, humidity, lux, co2);
    }
    
    return ESP_OK;
}

/**
 * @brief 更新时间显示
 */
esp_err_t ui_manager_update_time(uint8_t hour, uint8_t minute, uint8_t second)
{
    if (g_current_page == UI_PAGE_WATCHFACE) {
        return ui_watchface_update_time(hour, minute, second);
    }
    
    return ESP_OK;
}

/**
 * @brief 更新电池状态显示
 */
esp_err_t ui_manager_update_battery(uint8_t level, bool charging)
{
    // 所有页面都可能显示电池状态
    switch (g_current_page) {
        case UI_PAGE_WATCHFACE:
            return ui_watchface_update_battery(level, charging);
        case UI_PAGE_SETTINGS:
            return ui_settings_update_battery(level, charging);
        default:
            break;
    }
    
    return ESP_OK;
}

/**
 * @brief 显示通知消息
 */
esp_err_t ui_manager_show_notification(const char *title, const char *message, uint32_t duration_ms)
{
    if (!title || !message) {
        return ESP_ERR_INVALID_ARG;
    }
    
    ESP_LOGI(TAG, "显示通知: %s - %s", title, message);
    
    xSemaphoreTake(g_ui_mutex, portMAX_DELAY);
    
    // 创建通知弹窗
    lv_obj_t *msgbox = lv_msgbox_create(NULL, title, message, NULL, true);
    lv_obj_center(msgbox);
    
    xSemaphoreGive(g_ui_mutex);
    
    // TODO: 实现自动关闭定时器
    
    return ESP_OK;
}

/**
 * @brief 显示确认对话框
 */
esp_err_t ui_manager_show_confirm_dialog(const char *title, const char *message, void (*callback)(bool confirmed))
{
    if (!title || !message) {
        return ESP_ERR_INVALID_ARG;
    }
    
    ESP_LOGI(TAG, "显示确认对话框: %s - %s", title, message);
    
    xSemaphoreTake(g_ui_mutex, portMAX_DELAY);
    
    // 创建确认对话框
    static const char *btns[] = {"确定", "取消", ""};
    lv_obj_t *msgbox = lv_msgbox_create(NULL, title, message, btns, true);
    lv_obj_center(msgbox);
    
    // TODO: 设置回调函数
    
    xSemaphoreGive(g_ui_mutex);
    
    return ESP_OK;
}
