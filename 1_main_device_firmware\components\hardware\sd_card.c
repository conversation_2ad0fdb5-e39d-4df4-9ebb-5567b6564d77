/**
 * @file sd_card.c
 * @brief SD卡驱动程序
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "sd_card.h"
#include "hardware_hal.h"
#include "hardware_config.h"
#include "gpio_expander.h"
#include "esp_log.h"
#include "esp_vfs_fat.h"
#include "sdmmc_cmd.h"
#include "driver/sdmmc_host.h"
#include "driver/sdspi_host.h"

static const char *TAG = "SD_CARD";

/* SD卡状态 */
static bool g_sd_initialized = false;
static bool g_sd_mounted = false;
static sdmmc_card_t *g_sd_card = NULL;

/**
 * @brief SD卡初始化
 */
esp_err_t sd_card_init(void)
{
    if (g_sd_initialized) {
        ESP_LOGW(TAG, "SD卡已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化SD卡...");
    
    // 配置SD卡片选引脚
    esp_err_t ret = SD_CS_HIGH();  // 默认高电平
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "SD卡片选引脚配置失败");
        return ret;
    }
    
    // 配置SPI SD卡主机
    sdmmc_host_t host = SDSPI_HOST_DEFAULT();
    host.slot = SPI_HOST_ID;
    
    sdspi_device_config_t slot_config = SDSPI_DEVICE_CONFIG_DEFAULT();
    slot_config.gpio_cs = -1;  // 通过GPIO扩展芯片控制
    slot_config.host_id = SPI_HOST_ID;
    
    // 挂载配置
    esp_vfs_fat_sdmmc_mount_config_t mount_config = {
        .format_if_mount_failed = false,
        .max_files = 5,
        .allocation_unit_size = 16 * 1024
    };
    
    // 尝试挂载SD卡
    ret = esp_vfs_fat_sdspi_mount("/sdcard", &host, &slot_config, 
                                 &mount_config, &g_sd_card);
    
    if (ret == ESP_OK) {
        g_sd_mounted = true;
        ESP_LOGI(TAG, "SD卡挂载成功");
        ESP_LOGI(TAG, "SD卡信息:");
        ESP_LOGI(TAG, "  名称: %s", g_sd_card->cid.name);
        ESP_LOGI(TAG, "  类型: %s", (g_sd_card->ocr & SD_OCR_SDHC_CAP) ? "SDHC/SDXC" : "SDSC");
        ESP_LOGI(TAG, "  容量: %llu MB", ((uint64_t)g_sd_card->csd.capacity) * g_sd_card->csd.sector_size / (1024 * 1024));
        ESP_LOGI(TAG, "  速度: %d MHz", g_sd_card->real_freq_khz / 1000);
    } else if (ret == ESP_ERR_TIMEOUT) {
        ESP_LOGW(TAG, "SD卡未插入或无响应");
    } else {
        ESP_LOGE(TAG, "SD卡挂载失败: %s", esp_err_to_name(ret));
    }
    
    g_sd_initialized = true;
    ESP_LOGI(TAG, "SD卡初始化完成");
    
    return ESP_OK;  // 即使挂载失败也返回成功，因为SD卡是可选的
}

/**
 * @brief SD卡反初始化
 */
esp_err_t sd_card_deinit(void)
{
    if (!g_sd_initialized) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "反初始化SD卡...");
    
    if (g_sd_mounted) {
        esp_err_t ret = esp_vfs_fat_sdcard_unmount("/sdcard", g_sd_card);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "SD卡卸载失败: %s", esp_err_to_name(ret));
        } else {
            ESP_LOGI(TAG, "SD卡卸载成功");
        }
        g_sd_mounted = false;
        g_sd_card = NULL;
    }
    
    g_sd_initialized = false;
    ESP_LOGI(TAG, "SD卡反初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 检查SD卡是否挂载
 */
bool sd_card_is_mounted(void)
{
    return g_sd_mounted;
}
