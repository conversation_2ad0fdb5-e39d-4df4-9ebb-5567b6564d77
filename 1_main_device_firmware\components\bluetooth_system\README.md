# TIMO主体设备蓝牙通信系统

## 概述

本模块实现了TIMO智能闹钟主体设备与底座设备之间的蓝牙通信功能，支持氛围场景同步、音乐律动等核心功能。

## 功能特性

### 🔗 **核心通信功能**
- BLE客户端实现，主动连接底座设备
- 自动设备扫描和识别
- 稳定的连接管理和重连机制
- 完整的数据包协议实现
- 心跳保活机制

### 🎨 **氛围场景同步**
- 支持14种预定义氛围场景
- 自定义颜色和亮度控制
- 音乐律动模式支持
- 实时场景切换和同步

### 🎵 **音频律动功能**
- 基础律动模式
- 波浪律动模式  
- 频谱律动模式
- 实时音频数据传输

## 架构设计

### 模块组成

```
bluetooth_system/
├── include/
│   ├── bluetooth_system.h      # 主系统接口
│   ├── bt_client.h            # BLE客户端接口
│   ├── bt_protocol.h          # 通信协议定义
│   ├── bt_scene_sync.h        # 场景同步接口
│   └── bt_test.h              # 测试接口
├── bluetooth_system.c         # 主系统实现
├── bt_client.c               # BLE客户端实现
├── bt_protocol.c             # 协议实现
├── bt_scene_sync.c           # 场景同步实现
├── bt_test.c                 # 测试程序
└── CMakeLists.txt            # 构建配置
```

### 系统架构

```
┌─────────────────┐    ┌─────────────────┐
│   应用层        │    │   UI管理        │
│ (场景控制等)     │    │ (蓝牙管理界面)   │
└─────────────────┘    └─────────────────┘
         │                       │
┌─────────────────────────────────────────┐
│           蓝牙系统主模块                 │
│  - 连接管理  - 事件处理  - 数据路由     │
└─────────────────────────────────────────┘
         │                       │
┌─────────────────┐    ┌─────────────────┐
│   场景同步模块   │    │   BLE客户端     │
│ - 场景控制      │    │ - 设备扫描      │
│ - 音乐律动      │    │ - 连接管理      │
│ - 状态同步      │    │ - 数据传输      │
└─────────────────┘    └─────────────────┘
         │                       │
┌─────────────────────────────────────────┐
│              协议层                     │
│  - 数据包封装  - 校验和  - 错误处理     │
└─────────────────────────────────────────┘
         │
┌─────────────────────────────────────────┐
│            ESP32 BLE栈                  │
└─────────────────────────────────────────┘
```

## 通信协议

### 数据包格式

```
┌──────┬─────────┬─────┬─────┬─────┬────────┬─────────┬──────┐
│ 包头 │ 协议版本 │ 类型 │ 命令 │ 长度 │  数据   │ 校验和  │ 包尾 │
│ 2B   │   1B    │ 1B  │ 1B  │ 2B  │ 变长   │   1B   │ 2B   │
└──────┴─────────┴─────┴─────┴─────┴────────┴─────────┴──────┘
```

### 命令类型

| 类别 | 命令码 | 功能描述 |
|------|--------|----------|
| 系统命令 | 0x00-0x0F | 心跳、状态查询、配置等 |
| LED控制 | 0x10-0x1F | 场景设置、颜色控制、亮度调节 |
| 声音传感器 | 0x20-0x2F | 声音级别获取、阈值设置 |
| 按键控制 | 0x30-0x3F | 按键状态查询 |
| 氛围场景 | 0x40-0x4F | 场景切换、自定义场景 |
| 音乐律动 | 0x50-0x5F | 律动模式、音频数据传输 |

### 氛围场景定义

| 场景ID | 场景名称 | 描述 |
|--------|----------|------|
| 0 | 关闭模式 | LED完全关闭 |
| 1 | 待机模式 | 低亮度常亮 |
| 2 | 对话律动 | 语音交互时的律动效果 |
| 3 | 晨间唤醒 | 温暖的渐亮效果 |
| 4 | 小憩唤醒 | 柔和的唤醒效果 |
| 5 | 助眠模式 | 舒缓的助眠灯光 |
| 6 | 待办提醒 | 提醒用户的闪烁效果 |
| 7-9 | 预警模式 | 低/中/高级预警效果 |
| 10 | 专注模式 | 有助于专注的灯光 |
| 11 | 充电状态 | 充电时的指示灯效 |
| 12 | 配对模式 | 设备配对时的指示 |
| 13 | 自定义场景 | 用户自定义的颜色和效果 |

## API接口

### 主系统接口

```c
// 系统生命周期
esp_err_t bluetooth_system_init(void);
esp_err_t bluetooth_system_start(void);
esp_err_t bluetooth_system_stop(void);
esp_err_t bluetooth_system_deinit(void);

// 设备管理
esp_err_t bluetooth_system_start_scan(uint32_t timeout_s);
esp_err_t bluetooth_system_connect(const esp_bd_addr_t device_addr);
esp_err_t bluetooth_system_disconnect(void);

// 数据通信
esp_err_t bluetooth_system_send_command(uint8_t cmd, const uint8_t *data, uint16_t length);
bool bluetooth_system_is_connected(void);
bt_connection_state_t bluetooth_system_get_state(void);

// 回调注册
esp_err_t bluetooth_system_register_event_callback(bt_event_callback_t callback);
esp_err_t bluetooth_system_register_data_callback(bt_data_callback_t callback);
```

### 场景同步接口

```c
// 场景控制
esp_err_t bt_scene_sync_set_scene(bt_scene_id_t scene_id, uint8_t brightness, uint16_t duration_ms);
esp_err_t bt_scene_sync_set_custom_color(uint8_t red, uint8_t green, uint8_t blue, uint8_t brightness);

// 音乐律动
esp_err_t bt_scene_sync_start_music_mode(bt_music_mode_t mode);
esp_err_t bt_scene_sync_send_audio_data(uint8_t volume, const uint8_t *spectrum_data);
esp_err_t bt_scene_sync_stop_music_mode(void);

// 快捷场景
esp_err_t bt_scene_sync_standby(uint8_t brightness);
esp_err_t bt_scene_sync_conversation(void);
esp_err_t bt_scene_sync_morning_wake(void);
esp_err_t bt_scene_sync_sleep(void);
esp_err_t bt_scene_sync_warning(uint8_t level);
```

## 使用示例

### 基本连接流程

```c
// 1. 初始化系统
bluetooth_system_init();
bt_scene_sync_init();

// 2. 注册回调
bluetooth_system_register_event_callback(my_event_callback);
bluetooth_system_register_data_callback(my_data_callback);

// 3. 启动系统
bluetooth_system_start();

// 4. 扫描设备
bluetooth_system_start_scan(30);

// 5. 连接设备 (在扫描回调中)
bluetooth_system_connect(device_addr);

// 6. 使用场景同步
bt_scene_sync_standby(50);
```

### 场景控制示例

```c
// 设置晨间唤醒场景
bt_scene_sync_morning_wake();

// 设置自定义颜色
bt_scene_sync_set_custom_color(255, 100, 50, 80);

// 开始音乐律动
bt_scene_sync_start_music_mode(BT_MUSIC_MODE_SPECTRUM);

// 发送音频数据
uint8_t spectrum[8] = {10, 20, 30, 40, 50, 40, 30, 20};
bt_scene_sync_send_audio_data(75, spectrum);
```

## 集成说明

### 网络管理器集成

蓝牙系统已集成到网络管理器中，通过以下接口使用：

```c
// 启动蓝牙 (在系统管理器中自动调用)
network_manager_start_bluetooth();

// 连接到底座
network_manager_connect_to_base(device_addr);

// 获取连接状态
bool connected = network_manager_is_bluetooth_connected();
bt_connection_state_t state = network_manager_get_bluetooth_state();
```

### UI界面集成

提供了完整的蓝牙管理UI界面：

```c
// 创建蓝牙管理页面
lv_obj_t *page = ui_bluetooth_create_page(parent);

// 显示页面
ui_bluetooth_show_page();

// 更新设备列表
ui_bluetooth_update_device_list(devices, count);
```

## 测试验证

### 运行测试

```c
// 启动完整的蓝牙系统测试
bt_test_start();
```

测试程序会自动执行：
1. 协议功能测试
2. 设备扫描和连接
3. 场景同步功能测试
4. 长期稳定性测试

### 测试覆盖

- ✅ 协议数据包创建和解析
- ✅ 设备扫描和识别
- ✅ 连接建立和维护
- ✅ 所有氛围场景切换
- ✅ 自定义颜色控制
- ✅ 音乐律动模式
- ✅ 心跳保活机制
- ✅ 错误处理和恢复

## 状态说明

### ✅ 已完成功能

1. **完整的BLE客户端实现**
   - 设备扫描和过滤
   - 连接管理和重连
   - 服务发现和特征值操作

2. **完善的通信协议**
   - 数据包封装和解析
   - 校验和验证
   - 错误处理机制

3. **氛围场景同步系统**
   - 14种预定义场景
   - 自定义颜色控制
   - 音乐律动支持

4. **系统集成**
   - 网络管理器集成
   - UI界面支持
   - 完整的测试程序

### 🔄 后续优化

1. **性能优化**
   - 连接参数调优
   - 数据传输效率提升
   - 内存使用优化

2. **功能扩展**
   - 更多音乐律动模式
   - 场景自动切换
   - 智能亮度调节

3. **稳定性增强**
   - 异常恢复机制
   - 连接质量监控
   - 日志和诊断功能

## 依赖关系

- ESP-IDF BLE组件
- FreeRTOS任务和队列
- LVGL UI库 (可选)
- 系统管理器组件

## 注意事项

1. **内存管理**: 注意释放扫描结果和事件数据的内存
2. **线程安全**: 使用互斥锁保护共享数据
3. **错误处理**: 所有API调用都应检查返回值
4. **资源清理**: 系统关闭时正确清理所有资源

---

**版本**: 1.0.0  
**日期**: 2025-06-28  
**状态**: ✅ 完成并测试
