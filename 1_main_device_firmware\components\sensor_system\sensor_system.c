/**
 * @file sensor_system.c
 * @brief TIMO传感器系统实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "sensor_system.h"
#include "sensor_data_collector.h"
#include "sensor_monitor.h"
#include "environment_analyzer.h"
#include "sensor_calibration.h"
#include "system_manager.h"
#include "event_manager.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

static const char *TAG = "SENSOR_SYSTEM";

/* 传感器系统状态 */
static bool g_sensor_system_initialized = false;
static bool g_sensor_system_running = false;
static environment_data_t g_latest_data;
static sensor_config_t g_sensor_config;
static sensor_event_callback_t g_sensor_callback = NULL;

/* 任务和定时器 */
static TaskHandle_t g_sensor_task_handle = NULL;
static esp_timer_handle_t g_sample_timer = NULL;
static SemaphoreHandle_t g_sensor_mutex = NULL;

/* 默认配置 */
static const sensor_config_t DEFAULT_SENSOR_CONFIG = {
    .sample_interval_ms = 5000,     // 5秒采样间隔
    .auto_calibration = true,
    .data_logging = true,
    .alert_threshold_temp = 30,     // 30°C
    .alert_threshold_humi = 80,     // 80%
    .alert_threshold_co2 = 1000,    // 1000ppm
    .alert_threshold_lux = 50       // 50lux
};

/**
 * @brief 传感器采样定时器回调
 */
static void sensor_sample_timer_callback(void *arg)
{
    // 触发传感器读取
    sensor_system_trigger_read();
}

/**
 * @brief 传感器数据处理任务
 */
static void sensor_task(void *pvParameters)
{
    ESP_LOGI(TAG, "传感器数据处理任务启动");
    
    while (g_sensor_system_running) {
        // 等待数据采集完成的通知
        ulTaskNotifyTake(pdTRUE, pdMS_TO_TICKS(1000));
        
        if (!g_sensor_system_running) {
            break;
        }
        
        xSemaphoreTake(g_sensor_mutex, portMAX_DELAY);
        
        // 分析环境数据
        environment_status_t status = environment_analyzer_analyze(&g_latest_data);
        
        // 检查警报条件
        sensor_alert_t alert = SENSOR_ALERT_NONE;
        
        if (g_latest_data.temperature > g_sensor_config.alert_threshold_temp) {
            alert = SENSOR_ALERT_TEMP_HIGH;
        } else if (g_latest_data.temperature < 16.0f) {
            alert = SENSOR_ALERT_TEMP_LOW;
        } else if (g_latest_data.humidity > g_sensor_config.alert_threshold_humi) {
            alert = SENSOR_ALERT_HUMIDITY_HIGH;
        } else if (g_latest_data.humidity < 30.0f) {
            alert = SENSOR_ALERT_HUMIDITY_LOW;
        } else if (g_latest_data.co2 > g_sensor_config.alert_threshold_co2) {
            alert = SENSOR_ALERT_CO2_HIGH;
        } else if (g_latest_data.lux < g_sensor_config.alert_threshold_lux) {
            alert = SENSOR_ALERT_LIGHT_LOW;
        } else if (g_latest_data.lux > 2000.0f) {
            alert = SENSOR_ALERT_LIGHT_HIGH;
        }
        
        xSemaphoreGive(g_sensor_mutex);
        
        // 发送事件
        if (g_sensor_callback) {
            g_sensor_callback(&g_latest_data, alert);
        }
        
        if (alert != SENSOR_ALERT_NONE) {
            event_manager_post_event(EVENT_TYPE_SENSOR, alert, &g_latest_data);
        }
        
        // 记录数据
        if (g_sensor_config.data_logging) {
            sensor_monitor_log_data(&g_latest_data);
        }
    }
    
    ESP_LOGI(TAG, "传感器数据处理任务结束");
    vTaskDelete(NULL);
}

/**
 * @brief 初始化传感器系统
 */
esp_err_t sensor_system_init(void)
{
    if (g_sensor_system_initialized) {
        ESP_LOGW(TAG, "传感器系统已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化传感器系统...");
    
    // 创建互斥锁
    g_sensor_mutex = xSemaphoreCreateMutex();
    if (!g_sensor_mutex) {
        ESP_LOGE(TAG, "创建传感器互斥锁失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 初始化数据结构
    memset(&g_latest_data, 0, sizeof(environment_data_t));
    memcpy(&g_sensor_config, &DEFAULT_SENSOR_CONFIG, sizeof(sensor_config_t));
    
    // 初始化子模块
    esp_err_t ret = sensor_data_collector_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "传感器数据采集器初始化失败");
        return ret;
    }
    
    ret = sensor_monitor_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "传感器监控器初始化失败");
        return ret;
    }
    
    ret = environment_analyzer_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "环境分析器初始化失败");
        return ret;
    }
    
    ret = sensor_calibration_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "传感器校准模块初始化失败");
        return ret;
    }
    
    // 创建采样定时器
    esp_timer_create_args_t timer_args = {
        .callback = sensor_sample_timer_callback,
        .arg = NULL,
        .name = "sensor_sample_timer"
    };
    
    ret = esp_timer_create(&timer_args, &g_sample_timer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建采样定时器失败");
        return ret;
    }
    
    g_sensor_system_initialized = true;
    ESP_LOGI(TAG, "传感器系统初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 启动传感器系统
 */
esp_err_t sensor_system_start(void)
{
    if (!g_sensor_system_initialized) {
        ESP_LOGE(TAG, "传感器系统未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (g_sensor_system_running) {
        ESP_LOGW(TAG, "传感器系统已启动");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "启动传感器系统...");
    
    // 创建传感器任务
    BaseType_t ret = xTaskCreate(
        sensor_task,
        "sensor_task",
        4096,
        NULL,
        5,
        &g_sensor_task_handle
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建传感器任务失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 启动采样定时器
    esp_err_t timer_ret = esp_timer_start_periodic(g_sample_timer, 
                                                  g_sensor_config.sample_interval_ms * 1000);
    if (timer_ret != ESP_OK) {
        ESP_LOGE(TAG, "启动采样定时器失败");
        return timer_ret;
    }
    
    // 启动子模块
    sensor_data_collector_start();
    sensor_monitor_start();
    environment_analyzer_start();
    
    g_sensor_system_running = true;
    ESP_LOGI(TAG, "传感器系统启动完成，采样间隔: %d ms", g_sensor_config.sample_interval_ms);
    
    return ESP_OK;
}

/**
 * @brief 停止传感器系统
 */
esp_err_t sensor_system_stop(void)
{
    if (!g_sensor_system_running) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "停止传感器系统...");
    
    g_sensor_system_running = false;
    
    // 停止采样定时器
    if (g_sample_timer) {
        esp_timer_stop(g_sample_timer);
    }
    
    // 停止传感器任务
    if (g_sensor_task_handle) {
        xTaskNotifyGive(g_sensor_task_handle);  // 唤醒任务以便退出
        vTaskDelete(g_sensor_task_handle);
        g_sensor_task_handle = NULL;
    }
    
    // 停止子模块
    sensor_data_collector_stop();
    sensor_monitor_stop();
    environment_analyzer_stop();
    
    ESP_LOGI(TAG, "传感器系统停止完成");
    return ESP_OK;
}

/**
 * @brief 获取最新环境数据
 */
environment_data_t* sensor_system_get_latest_data(void)
{
    return &g_latest_data;
}

/**
 * @brief 获取环境状态评估
 */
environment_status_t sensor_system_get_environment_status(void)
{
    return environment_analyzer_analyze(&g_latest_data);
}

/**
 * @brief 设置传感器配置
 */
esp_err_t sensor_system_set_config(const sensor_config_t *config)
{
    if (!config) {
        return ESP_ERR_INVALID_ARG;
    }
    
    xSemaphoreTake(g_sensor_mutex, portMAX_DELAY);
    memcpy(&g_sensor_config, config, sizeof(sensor_config_t));
    xSemaphoreGive(g_sensor_mutex);
    
    // 重新启动采样定时器
    if (g_sample_timer && g_sensor_system_running) {
        esp_timer_stop(g_sample_timer);
        esp_timer_start_periodic(g_sample_timer, g_sensor_config.sample_interval_ms * 1000);
    }
    
    ESP_LOGI(TAG, "传感器配置已更新");
    return ESP_OK;
}

/**
 * @brief 获取传感器配置
 */
sensor_config_t* sensor_system_get_config(void)
{
    return &g_sensor_config;
}

/**
 * @brief 注册传感器事件回调
 */
esp_err_t sensor_system_register_callback(sensor_event_callback_t callback)
{
    g_sensor_callback = callback;
    return ESP_OK;
}

/**
 * @brief 手动触发传感器读取
 */
esp_err_t sensor_system_trigger_read(void)
{
    if (!g_sensor_system_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGD(TAG, "手动触发传感器读取");
    
    // 采集传感器数据
    esp_err_t ret = sensor_data_collector_read_all(&g_latest_data);
    if (ret == ESP_OK) {
        g_latest_data.timestamp = esp_timer_get_time();
        g_latest_data.valid = true;
        
        // 通知处理任务
        if (g_sensor_task_handle) {
            xTaskNotifyGive(g_sensor_task_handle);
        }
    } else {
        ESP_LOGW(TAG, "传感器数据读取失败");
        g_latest_data.valid = false;
    }
    
    return ret;
}

/**
 * @brief 校准传感器
 */
esp_err_t sensor_system_calibrate(void)
{
    ESP_LOGI(TAG, "开始传感器校准...");
    
    esp_err_t ret = sensor_calibration_calibrate_all();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "传感器校准完成");
    } else {
        ESP_LOGE(TAG, "传感器校准失败");
    }
    
    return ret;
}

/**
 * @brief 获取传感器状态
 */
bool sensor_system_get_sensor_status(const char *sensor_name)
{
    if (!sensor_name) {
        return false;
    }
    
    // 检查传感器是否在线
    if (strcmp(sensor_name, "AHT30") == 0) {
        return i2c_bus_device_online(AHT30_I2C_ADDR);
    } else if (strcmp(sensor_name, "BH1750") == 0) {
        return i2c_bus_device_online(BH1750_I2C_ADDR);
    } else if (strcmp(sensor_name, "SGP30") == 0) {
        return i2c_bus_device_online(SGP30_I2C_ADDR);
    } else if (strcmp(sensor_name, "QMI8658") == 0) {
        return i2c_bus_device_online(QMI8658_I2C_ADDR);
    }
    
    return false;
}

/**
 * @brief 获取环境舒适度评分
 */
uint8_t sensor_system_get_comfort_score(void)
{
    return environment_analyzer_get_comfort_score(&g_latest_data);
}

/**
 * @brief 获取空气质量指数
 */
uint16_t sensor_system_get_air_quality_index(void)
{
    return environment_analyzer_get_aqi(&g_latest_data);
}

/**
 * @brief 检查是否需要开窗通风
 */
bool sensor_system_need_ventilation(void)
{
    return environment_analyzer_need_ventilation(&g_latest_data);
}

/**
 * @brief 获取建议信息
 */
esp_err_t sensor_system_get_suggestions(char *buffer, size_t size)
{
    if (!buffer || size == 0) {
        return ESP_ERR_INVALID_ARG;
    }
    
    return environment_analyzer_get_suggestions(&g_latest_data, buffer, size);
}
