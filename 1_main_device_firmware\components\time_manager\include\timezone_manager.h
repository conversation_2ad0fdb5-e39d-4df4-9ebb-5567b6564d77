/**
 * @file timezone_manager.h
 * @brief TIMO时区管理器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef TIMEZONE_MANAGER_H
#define TIMEZONE_MANAGER_H

#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化时区管理器
 * @return esp_err_t 
 */
esp_err_t timezone_manager_init(void);

/**
 * @brief 设置时区
 * @param timezone 时区字符串
 * @return esp_err_t 
 */
esp_err_t timezone_manager_set_timezone(const char *timezone);

/**
 * @brief 获取当前时区
 * @return const char* 
 */
const char* timezone_manager_get_timezone(void);

#ifdef __cplusplus
}
#endif

#endif /* TIMEZONE_MANAGER_H */
