/**
 * @file ui_bluetooth.h
 * @brief TIMO蓝牙管理UI界面头文件
 * @version 1.0.0
 * @date 2025-06-28
 */

#ifndef UI_BLUETOOTH_H
#define UI_BLUETOOTH_H

#include "lvgl.h"
#include "esp_err.h"
#include "bluetooth_system.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化蓝牙管理UI
 * @return esp_err_t 
 */
esp_err_t ui_bluetooth_init(void);

/**
 * @brief 创建蓝牙管理页面
 * @param parent 父容器
 * @return lv_obj_t* 页面对象
 */
lv_obj_t* ui_bluetooth_create_page(lv_obj_t *parent);

/**
 * @brief 显示蓝牙管理页面
 */
void ui_bluetooth_show_page(void);

/**
 * @brief 隐藏蓝牙管理页面
 */
void ui_bluetooth_hide_page(void);

/**
 * @brief 更新蓝牙连接状态显示
 * @param state 连接状态
 */
void ui_bluetooth_update_status(bt_connection_state_t state);

/**
 * @brief 更新扫描到的设备列表
 * @param devices 设备列表
 * @param count 设备数量
 */
void ui_bluetooth_update_device_list(const bt_device_info_t *devices, uint8_t count);

/**
 * @brief 显示连接进度
 * @param connecting 是否正在连接
 */
void ui_bluetooth_show_connecting(bool connecting);

/**
 * @brief 显示扫描进度
 * @param scanning 是否正在扫描
 */
void ui_bluetooth_show_scanning(bool scanning);

#ifdef __cplusplus
}
#endif

#endif /* UI_BLUETOOTH_H */
