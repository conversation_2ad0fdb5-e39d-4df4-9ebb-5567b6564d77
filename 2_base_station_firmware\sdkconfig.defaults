# TIMO底座设备固件配置 (ESP32-C2)

# 芯片配置
CONFIG_IDF_TARGET="esp32c2"
CONFIG_IDF_TARGET_ESP32C2=y

# 应用程序配置
CONFIG_APP_PROJECT_VER="1.0.0"

# 系统配置
CONFIG_ESP_MAIN_TASK_STACK_SIZE=4096
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=2048

# 内存配置
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_120=y
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ=120

# 蓝牙配置
CONFIG_BT_ENABLED=y
CONFIG_BT_BLE_ENABLED=y
CONFIG_BT_CONTROLLER_ENABLED=y
CONFIG_BT_BLUEDROID_ENABLED=y
CONFIG_BT_BLE_42_FEATURES_SUPPORTED=y
CONFIG_BT_GATTS_ENABLE=y
CONFIG_BT_GATT_MAX_SR_PROFILES=8
CONFIG_BT_GATT_MAX_SR_ATTRIBUTES=100

# ADC配置
CONFIG_ADC_ONESHOT_CTRL_FUNC_IN_IRAM=y
CONFIG_ADC_CAL_EFUSE_TP_ENABLE=y
CONFIG_ADC_CAL_EFUSE_VREF_ENABLE=y
CONFIG_ADC_CAL_LUT_ENABLE=y

# GPIO配置
CONFIG_GPIO_ESP32_SUPPORT_SWITCH_SLP_PULL=y

# RMT配置 (用于WS2812)
CONFIG_RMT_ENABLE_TX_LOOP_COUNT=y

# 电源管理
CONFIG_PM_ENABLE=y
CONFIG_PM_DFS_INIT_AUTO=y
CONFIG_PM_USE_RTC_TIMER_REF=y

# 日志配置
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_LOG_DEFAULT_LEVEL=3
CONFIG_LOG_MAXIMUM_LEVEL=5

# FreeRTOS配置
CONFIG_FREERTOS_HZ=1000
CONFIG_FREERTOS_ASSERT_ON_UNTESTED_FUNCTION=y
CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL=y

# 网络配置 (虽然底座不使用WiFi，但保留基础配置)
CONFIG_ESP_NETIF_TCPIP_LWIP=y

# NVS配置
CONFIG_NVS_ENCRYPTION=n

# 分区表
CONFIG_PARTITION_TABLE_SINGLE_APP=y

# 编译器优化
CONFIG_COMPILER_OPTIMIZATION_SIZE=y
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE=y

# 看门狗配置
CONFIG_ESP_TASK_WDT=y
CONFIG_ESP_TASK_WDT_TIMEOUT_S=5
CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0=y

# 堆内存配置
CONFIG_HEAP_POISONING_DISABLED=y
CONFIG_HEAP_TRACING_OFF=y

# 启动配置
CONFIG_BOOTLOADER_LOG_LEVEL_INFO=y
CONFIG_BOOTLOADER_LOG_LEVEL=3

# 安全配置
CONFIG_SECURE_BOOT_V2_ENABLED=n
CONFIG_SECURE_FLASH_ENC_ENABLED=n

# 调试配置
CONFIG_ESP_ERR_TO_NAME_LOOKUP=y
CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT=y

# LED Strip配置
CONFIG_LED_STRIP_RMT_INTR_PRIORITY=1
