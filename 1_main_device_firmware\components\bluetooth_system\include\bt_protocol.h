/**
 * @file bt_protocol.h
 * @brief TIMO蓝牙通信协议定义
 * @version 1.0.0
 * @date 2025-06-28
 */

#ifndef BT_PROTOCOL_H
#define BT_PROTOCOL_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 协议版本 */
#define BT_PROTOCOL_VERSION         0x01

/* 数据包头部标识 */
#define BT_PACKET_HEADER            0xAA55
#define BT_PACKET_FOOTER            0x55AA

/* 命令类型定义 */
typedef enum {
    /* 系统命令 0x00-0x0F */
    BT_CMD_PING = 0x00,                 // 心跳包
    BT_CMD_PONG = 0x01,                 // 心跳响应
    BT_CMD_GET_STATUS = 0x02,           // 获取状态
    BT_CMD_SET_CONFIG = 0x03,           // 设置配置
    BT_CMD_RESET = 0x04,                // 重置设备
    BT_CMD_VERSION = 0x05,              // 获取版本信息
    
    /* LED控制命令 0x10-0x1F */
    BT_CMD_LED_SET_SCENE = 0x10,        // 设置氛围场景
    BT_CMD_LED_SET_COLOR = 0x11,        // 设置颜色
    BT_CMD_LED_SET_BRIGHTNESS = 0x12,   // 设置亮度
    BT_CMD_LED_SET_EFFECT = 0x13,       // 设置特效
    BT_CMD_LED_TURN_ON = 0x14,          // 开启LED
    BT_CMD_LED_TURN_OFF = 0x15,         // 关闭LED
    
    /* 声音传感器命令 0x20-0x2F */
    BT_CMD_SOUND_GET_LEVEL = 0x20,      // 获取声音级别
    BT_CMD_SOUND_SET_THRESHOLD = 0x21,  // 设置阈值
    BT_CMD_SOUND_GET_STATS = 0x22,      // 获取统计信息
    
    /* 按键命令 0x30-0x3F */
    BT_CMD_BUTTON_GET_STATE = 0x30,     // 获取按键状态
    
    /* 氛围场景命令 0x40-0x4F */
    BT_CMD_SCENE_SWITCH = 0x40,         // 切换场景
    BT_CMD_SCENE_GET_LIST = 0x41,       // 获取场景列表
    BT_CMD_SCENE_SET_CUSTOM = 0x42,     // 设置自定义场景
    
    /* 音乐律动命令 0x50-0x5F */
    BT_CMD_MUSIC_START = 0x50,          // 开始音乐律动
    BT_CMD_MUSIC_STOP = 0x51,           // 停止音乐律动
    BT_CMD_MUSIC_SET_MODE = 0x52,       // 设置律动模式
    BT_CMD_MUSIC_SEND_DATA = 0x53,      // 发送音频数据
    
    /* 错误响应 */
    BT_CMD_ERROR = 0xFF                 // 错误响应
} bt_command_t;

/* 氛围场景ID定义 (与底座保持一致) */
typedef enum {
    BT_SCENE_OFF = 0,                   // 关闭模式
    BT_SCENE_STANDBY = 1,               // 待机模式
    BT_SCENE_CONVERSATION = 2,          // 对话律动
    BT_SCENE_MORNING_WAKE = 3,          // 晨间唤醒
    BT_SCENE_NAP_WAKE = 4,              // 小憩唤醒
    BT_SCENE_SLEEP = 5,                 // 助眠模式
    BT_SCENE_TODO_REMINDER = 6,         // 待办提醒
    BT_SCENE_WARNING_LOW = 7,           // 低级预警
    BT_SCENE_WARNING_MID = 8,           // 中级预警
    BT_SCENE_WARNING_HIGH = 9,          // 高级预警
    BT_SCENE_FOCUS = 10,                // 专注模式
    BT_SCENE_CHARGING = 11,             // 充电状态
    BT_SCENE_PAIRING = 12,              // 配对模式
    BT_SCENE_CUSTOM = 13,               // 自定义场景
    BT_SCENE_MAX = 14
} bt_scene_id_t;

/* 音乐律动模式 */
typedef enum {
    BT_MUSIC_MODE_BASIC = 0,            // 基础律动模式
    BT_MUSIC_MODE_WAVE = 1,             // 波浪律动模式
    BT_MUSIC_MODE_SPECTRUM = 2          // 频谱律动模式
} bt_music_mode_t;

/* 错误码定义 */
typedef enum {
    BT_ERROR_NONE = 0x00,               // 无错误
    BT_ERROR_INVALID_CMD = 0x01,        // 无效命令
    BT_ERROR_INVALID_PARAM = 0x02,      // 无效参数
    BT_ERROR_NOT_SUPPORTED = 0x03,      // 不支持的操作
    BT_ERROR_BUSY = 0x04,               // 设备忙
    BT_ERROR_TIMEOUT = 0x05,            // 超时
    BT_ERROR_HARDWARE = 0x06,           // 硬件错误
    BT_ERROR_UNKNOWN = 0xFF             // 未知错误
} bt_error_code_t;

/* 数据包结构 */
typedef struct __attribute__((packed)) {
    uint16_t header;                    // 包头标识 0xAA55
    uint8_t version;                    // 协议版本
    uint8_t type;                       // 数据类型
    uint8_t cmd;                        // 命令码
    uint16_t length;                    // 数据长度
    uint8_t data[];                     // 数据内容 (变长)
    // uint8_t checksum;                // 校验和 (在数据末尾)
    // uint16_t footer;                 // 包尾标识 0x55AA (在校验和后)
} bt_packet_header_t;

/* LED颜色结构 */
typedef struct __attribute__((packed)) {
    uint8_t red;                        // 红色分量
    uint8_t green;                      // 绿色分量
    uint8_t blue;                       // 蓝色分量
    uint8_t brightness;                 // 亮度 (0-100)
} bt_led_color_t;

/* LED场景参数 */
typedef struct __attribute__((packed)) {
    uint8_t scene_id;                   // 场景ID
    uint8_t brightness;                 // 亮度 (0-100)
    uint16_t duration_ms;               // 持续时间 (毫秒)
    bt_led_color_t color;               // 颜色参数
} bt_scene_params_t;

/* 音频数据包 */
typedef struct __attribute__((packed)) {
    uint8_t mode;                       // 律动模式
    uint8_t volume;                     // 音量级别 (0-100)
    uint16_t frequency;                 // 主频率
    uint8_t spectrum[8];                // 频谱数据 (8个频段)
} bt_audio_data_t;

/* 设备状态信息 */
typedef struct __attribute__((packed)) {
    uint8_t led_status;                 // LED状态
    uint8_t current_scene;              // 当前场景
    uint8_t brightness;                 // 当前亮度
    uint16_t sound_level;               // 声音级别
    uint8_t button_state;               // 按键状态
    uint32_t uptime;                    // 运行时间 (秒)
    uint8_t error_code;                 // 错误码
} bt_device_status_t;

/* 版本信息 */
typedef struct __attribute__((packed)) {
    uint8_t major;                      // 主版本号
    uint8_t minor;                      // 次版本号
    uint8_t patch;                      // 补丁版本号
    uint32_t build;                     // 构建号
    char git_hash[8];                   // Git哈希值
} bt_version_info_t;

/**
 * @brief 创建数据包
 * @param type 数据类型
 * @param cmd 命令码
 * @param data 数据内容
 * @param data_len 数据长度
 * @param packet 输出数据包缓冲区
 * @param packet_size 数据包缓冲区大小
 * @return uint16_t 实际数据包长度，0表示失败
 */
uint16_t bt_protocol_create_packet(uint8_t type, uint8_t cmd, 
                                  const uint8_t *data, uint16_t data_len,
                                  uint8_t *packet, uint16_t packet_size);

/**
 * @brief 解析数据包
 * @param packet 数据包
 * @param packet_len 数据包长度
 * @param header 输出包头信息
 * @param payload 输出数据内容指针
 * @param payload_len 输出数据长度
 * @return true 解析成功
 * @return false 解析失败
 */
bool bt_protocol_parse_packet(const uint8_t *packet, uint16_t packet_len,
                             bt_packet_header_t *header, 
                             const uint8_t **payload, uint16_t *payload_len);

/**
 * @brief 计算校验和
 * @param data 数据
 * @param length 数据长度
 * @return uint8_t 校验和
 */
uint8_t bt_protocol_calculate_checksum(const uint8_t *data, uint16_t length);

/**
 * @brief 验证数据包校验和
 * @param packet 数据包
 * @param packet_len 数据包长度
 * @return true 校验成功
 * @return false 校验失败
 */
bool bt_protocol_verify_checksum(const uint8_t *packet, uint16_t packet_len);

#ifdef __cplusplus
}
#endif

#endif /* BT_PROTOCOL_H */
