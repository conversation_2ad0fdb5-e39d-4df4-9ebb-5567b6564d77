/**
 * @file sensor_data_collector.c
 * @brief TIMO传感器数据采集器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "sensor_data_collector.h"
#include "hardware_hal.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "SENSOR_COLLECTOR";

static bool g_collector_initialized = false;
static bool g_collector_running = false;

/**
 * @brief 初始化传感器数据采集器
 */
esp_err_t sensor_data_collector_init(void)
{
    if (g_collector_initialized) {
        ESP_LOGW(TAG, "传感器数据采集器已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化传感器数据采集器...");
    
    // 传感器已在硬件层初始化
    
    g_collector_initialized = true;
    ESP_LOGI(TAG, "传感器数据采集器初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 启动传感器数据采集器
 */
esp_err_t sensor_data_collector_start(void)
{
    if (!g_collector_initialized) {
        ESP_LOGE(TAG, "传感器数据采集器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "启动传感器数据采集器...");
    
    g_collector_running = true;
    ESP_LOGI(TAG, "传感器数据采集器启动完成");
    
    return ESP_OK;
}

/**
 * @brief 停止传感器数据采集器
 */
esp_err_t sensor_data_collector_stop(void)
{
    if (!g_collector_running) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "停止传感器数据采集器...");
    
    g_collector_running = false;
    ESP_LOGI(TAG, "传感器数据采集器停止完成");
    
    return ESP_OK;
}

/**
 * @brief 读取所有传感器数据
 */
esp_err_t sensor_data_collector_read_all(environment_data_t *data)
{
    if (!data) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_collector_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGD(TAG, "读取所有传感器数据...");
    
    esp_err_t ret;
    bool any_success = false;
    
    // 读取AHT30温湿度传感器
    aht30_data_t aht30_data;
    ret = aht30_read(&aht30_data);
    if (ret == ESP_OK) {
        data->temperature = aht30_data.temperature;
        data->humidity = aht30_data.humidity;
        any_success = true;
        ESP_LOGD(TAG, "AHT30: 温度=%.1f°C, 湿度=%.1f%%", 
                 data->temperature, data->humidity);
    } else {
        ESP_LOGW(TAG, "AHT30读取失败");
        data->temperature = 0.0f;
        data->humidity = 0.0f;
    }
    
    // 读取BH1750光照传感器
    bh1750_data_t bh1750_data;
    ret = bh1750_read(&bh1750_data);
    if (ret == ESP_OK) {
        data->lux = bh1750_data.lux;
        any_success = true;
        ESP_LOGD(TAG, "BH1750: 光照=%.1f lux", data->lux);
    } else {
        ESP_LOGW(TAG, "BH1750读取失败");
        data->lux = 0.0f;
    }
    
    // 读取SGP30 CO2传感器
    sgp30_data_t sgp30_data;
    ret = sgp30_read(&sgp30_data);
    if (ret == ESP_OK) {
        data->co2 = sgp30_data.co2;
        data->tvoc = sgp30_data.tvoc;
        any_success = true;
        ESP_LOGD(TAG, "SGP30: CO2=%d ppm, TVOC=%d ppb", 
                 data->co2, data->tvoc);
    } else {
        ESP_LOGW(TAG, "SGP30读取失败");
        data->co2 = 0;
        data->tvoc = 0;
    }
    
    // 读取QMI8658姿态传感器
    qmi8658_data_t qmi8658_data;
    ret = qmi8658_read(&qmi8658_data);
    if (ret == ESP_OK) {
        data->acc_x = qmi8658_data.acc_x;
        data->acc_y = qmi8658_data.acc_y;
        data->acc_z = qmi8658_data.acc_z;
        data->gyro_x = qmi8658_data.gyro_x;
        data->gyro_y = qmi8658_data.gyro_y;
        data->gyro_z = qmi8658_data.gyro_z;
        any_success = true;
        ESP_LOGD(TAG, "QMI8658: 加速度=(%.2f,%.2f,%.2f)g, 角速度=(%.1f,%.1f,%.1f)dps",
                 data->acc_x, data->acc_y, data->acc_z,
                 data->gyro_x, data->gyro_y, data->gyro_z);
    } else {
        ESP_LOGW(TAG, "QMI8658读取失败");
        data->acc_x = data->acc_y = data->acc_z = 0.0f;
        data->gyro_x = data->gyro_y = data->gyro_z = 0.0f;
    }
    
    if (any_success) {
        ESP_LOGD(TAG, "传感器数据读取完成");
        return ESP_OK;
    } else {
        ESP_LOGE(TAG, "所有传感器读取失败");
        return ESP_FAIL;
    }
}
