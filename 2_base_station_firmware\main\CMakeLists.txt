idf_component_register(
    SRCS
        "main.c"
        "base_main.c"
        "ws2812_driver.c"
        "sound_sensor.c"
        "button_handler.c"
        "bluetooth_comm.c"
        "ambient_effects.c"
    INCLUDE_DIRS 
        "."
        "include"
    REQUIRES 
        driver
        esp_wifi
        esp_netif
        esp_event
        nvs_flash
        bt
        esp_bt
        esp_gatt
        esp_gap_ble
        esp_gattc
        esp_gatts
        esp_timer
        esp_adc
        esp_pm
        esp_sleep
        esp_system
        esp_common
        freertos
        log
        spi_flash
        esp_partition
        esp_hw_support
        hal
        soc
        esp_rom
        esp_mm
        heap
        led_strip
)
