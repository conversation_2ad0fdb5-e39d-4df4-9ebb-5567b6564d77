/**
 * @file ntp_client.c
 * @brief TIMO NTP客户端实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "ntp_client.h"
#include "esp_log.h"
#include "esp_sntp.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include <sys/time.h>

static const char *TAG = "NTP_CLIENT";

static bool g_ntp_initialized = false;
static EventGroupHandle_t g_ntp_event_group = NULL;

#define NTP_SYNC_BIT BIT0

/**
 * @brief SNTP同步回调
 */
static void sntp_sync_time_cb(struct timeval *tv)
{
    ESP_LOGI(TAG, "NTP时间同步成功");
    if (g_ntp_event_group) {
        xEventGroupSetBits(g_ntp_event_group, NTP_SYNC_BIT);
    }
}

/**
 * @brief 初始化NTP客户端
 */
esp_err_t ntp_client_init(void)
{
    if (g_ntp_initialized) {
        ESP_LOGW(TAG, "NTP客户端已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化NTP客户端...");
    
    g_ntp_event_group = xEventGroupCreate();
    if (!g_ntp_event_group) {
        ESP_LOGE(TAG, "创建NTP事件组失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 初始化SNTP
    sntp_setoperatingmode(SNTP_OPMODE_POLL);
    sntp_set_sync_mode(SNTP_SYNC_MODE_IMMED);
    sntp_set_time_sync_notification_cb(sntp_sync_time_cb);
    
    g_ntp_initialized = true;
    ESP_LOGI(TAG, "NTP客户端初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 同步NTP时间
 */
esp_err_t ntp_client_sync(const char *server)
{
    if (!g_ntp_initialized) {
        ESP_LOGE(TAG, "NTP客户端未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (!server) {
        server = "pool.ntp.org";
    }
    
    ESP_LOGI(TAG, "开始NTP同步，服务器: %s", server);
    
    // 清除同步标志
    xEventGroupClearBits(g_ntp_event_group, NTP_SYNC_BIT);
    
    // 设置NTP服务器
    sntp_setservername(0, server);
    sntp_init();
    
    // 等待同步完成
    EventBits_t bits = xEventGroupWaitBits(
        g_ntp_event_group,
        NTP_SYNC_BIT,
        pdTRUE,
        pdFALSE,
        pdMS_TO_TICKS(10000)  // 10秒超时
    );
    
    sntp_stop();
    
    if (bits & NTP_SYNC_BIT) {
        ESP_LOGI(TAG, "NTP同步成功");
        return ESP_OK;
    } else {
        ESP_LOGE(TAG, "NTP同步超时");
        return ESP_ERR_TIMEOUT;
    }
}
