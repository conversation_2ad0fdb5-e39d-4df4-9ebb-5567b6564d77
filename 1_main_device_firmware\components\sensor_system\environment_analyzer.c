/**
 * @file environment_analyzer.c
 * @brief TIMO环境分析器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "environment_analyzer.h"
#include "esp_log.h"
#include <stdio.h>

static const char *TAG = "ENV_ANALYZER";

esp_err_t environment_analyzer_init(void)
{
    ESP_LOGI(TAG, "初始化环境分析器...");
    return ESP_OK;
}

esp_err_t environment_analyzer_start(void)
{
    ESP_LOGI(TAG, "启动环境分析器...");
    return ESP_OK;
}

esp_err_t environment_analyzer_stop(void)
{
    ESP_LOGI(TAG, "停止环境分析器...");
    return ESP_OK;
}

environment_status_t environment_analyzer_analyze(const environment_data_t *data)
{
    if (!data || !data->valid) {
        return ENV_STATUS_POOR;
    }
    
    // 简单的环境评估算法
    int score = 0;
    
    // 温度评分 (18-26°C为最佳)
    if (data->temperature >= 18.0f && data->temperature <= 26.0f) {
        score += 25;
    } else if (data->temperature >= 16.0f && data->temperature <= 30.0f) {
        score += 15;
    } else {
        score += 5;
    }
    
    // 湿度评分 (40-60%为最佳)
    if (data->humidity >= 40.0f && data->humidity <= 60.0f) {
        score += 25;
    } else if (data->humidity >= 30.0f && data->humidity <= 70.0f) {
        score += 15;
    } else {
        score += 5;
    }
    
    // CO2评分 (<800ppm为最佳)
    if (data->co2 < 800) {
        score += 25;
    } else if (data->co2 < 1000) {
        score += 15;
    } else if (data->co2 < 1500) {
        score += 10;
    } else {
        score += 0;
    }
    
    // 光照评分 (200-1000lux为最佳)
    if (data->lux >= 200.0f && data->lux <= 1000.0f) {
        score += 25;
    } else if (data->lux >= 100.0f && data->lux <= 2000.0f) {
        score += 15;
    } else {
        score += 5;
    }
    
    // 根据总分评估环境状态
    if (score >= 80) {
        return ENV_STATUS_EXCELLENT;
    } else if (score >= 60) {
        return ENV_STATUS_GOOD;
    } else if (score >= 40) {
        return ENV_STATUS_MODERATE;
    } else if (score >= 20) {
        return ENV_STATUS_POOR;
    } else {
        return ENV_STATUS_UNHEALTHY;
    }
}

uint8_t environment_analyzer_get_comfort_score(const environment_data_t *data)
{
    if (!data || !data->valid) {
        return 0;
    }
    
    environment_status_t status = environment_analyzer_analyze(data);
    
    switch (status) {
        case ENV_STATUS_EXCELLENT:  return 95;
        case ENV_STATUS_GOOD:       return 80;
        case ENV_STATUS_MODERATE:   return 60;
        case ENV_STATUS_POOR:       return 40;
        case ENV_STATUS_UNHEALTHY:  return 20;
        default:                    return 0;
    }
}

uint16_t environment_analyzer_get_aqi(const environment_data_t *data)
{
    if (!data || !data->valid) {
        return 500;  // 严重污染
    }
    
    // 简化的AQI计算，主要基于CO2和TVOC
    uint16_t aqi = 50;  // 基础值
    
    if (data->co2 > 1000) {
        aqi += (data->co2 - 1000) / 10;
    }
    
    if (data->tvoc > 220) {
        aqi += (data->tvoc - 220) / 5;
    }
    
    if (aqi > 500) aqi = 500;
    
    return aqi;
}

bool environment_analyzer_need_ventilation(const environment_data_t *data)
{
    if (!data || !data->valid) {
        return false;
    }
    
    return (data->co2 > 1000 || data->tvoc > 300 || data->humidity > 70.0f);
}

esp_err_t environment_analyzer_get_suggestions(const environment_data_t *data, char *buffer, size_t size)
{
    if (!data || !buffer || size == 0) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!data->valid) {
        snprintf(buffer, size, "传感器数据无效");
        return ESP_OK;
    }
    
    char suggestions[256] = "";
    
    if (data->temperature > 26.0f) {
        strcat(suggestions, "温度偏高，建议开启空调或风扇；");
    } else if (data->temperature < 18.0f) {
        strcat(suggestions, "温度偏低，建议增加衣物或开启暖气；");
    }
    
    if (data->humidity > 60.0f) {
        strcat(suggestions, "湿度偏高，建议开启除湿器；");
    } else if (data->humidity < 40.0f) {
        strcat(suggestions, "湿度偏低，建议开启加湿器；");
    }
    
    if (data->co2 > 1000) {
        strcat(suggestions, "CO2浓度过高，建议开窗通风；");
    }
    
    if (data->lux < 200.0f) {
        strcat(suggestions, "光照不足，建议开启照明；");
    }
    
    if (strlen(suggestions) == 0) {
        strcat(suggestions, "环境状况良好，无需调整。");
    }
    
    snprintf(buffer, size, "%s", suggestions);
    return ESP_OK;
}
