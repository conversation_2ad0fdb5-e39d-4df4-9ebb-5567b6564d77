/**
 * @file sensor_data_collector.h
 * @brief TIMO传感器数据采集器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef SENSOR_DATA_COLLECTOR_H
#define SENSOR_DATA_COLLECTOR_H

#include "esp_err.h"
#include "sensor_system.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化传感器数据采集器
 * @return esp_err_t 
 */
esp_err_t sensor_data_collector_init(void);

/**
 * @brief 启动传感器数据采集器
 * @return esp_err_t 
 */
esp_err_t sensor_data_collector_start(void);

/**
 * @brief 停止传感器数据采集器
 * @return esp_err_t 
 */
esp_err_t sensor_data_collector_stop(void);

/**
 * @brief 读取所有传感器数据
 * @param data 环境数据结构体
 * @return esp_err_t 
 */
esp_err_t sensor_data_collector_read_all(environment_data_t *data);

#ifdef __cplusplus
}
#endif

#endif /* SENSOR_DATA_COLLECTOR_H */
