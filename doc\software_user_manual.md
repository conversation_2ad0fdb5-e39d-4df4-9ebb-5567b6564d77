# TIMO智能闹钟软件使用说明

## 一、项目概述

TIMO智能闹钟是一个基于ESP32-S3和ESP32-C2的双设备智能终端系统，具备语音交互、环境监测、氛围灯效、时间管理等多种功能。

### 系统架构
- **主体设备**: ESP32-S3N16R8 (16MB Flash + 8MB PSRAM)
- **底座设备**: ESP32-C2 (2MB Flash)
- **通信方式**: 蓝牙5.0连接
- **开发框架**: ESP-IDF + LVGL UI

### 主要功能
1. 480×480圆形LCD触摸屏显示
2. 多传感器环境监测 (温湿度、光照、CO2、姿态)
3. 双麦克风阵列语音交互
4. WS2812 RGB氛围灯效 (30颗LED)
5. 多闹钟管理和番茄时钟
6. SD卡音乐播放
7. WiFi网络连接和时间同步

## 二、硬件连接说明

### 主体设备引脚分配

#### I2C总线 (共享)
- **SCL**: GPIO7
- **SDA**: GPIO15
- **设备列表**:
  - 0x18: ES8311 音频输出
  - 0x20: TCA9554PWR GPIO扩展
  - 0x23: BH1750 光照传感器
  - 0x38: AHT30 温湿度传感器
  - 0x41: ES7210 音频输入
  - 0x51: PCF85063 RTC时钟
  - 0x58: SGP30 CO2传感器
  - 0x5D: GT911 触摸屏
  - 0x6A: QMI8658 姿态传感器

#### SPI总线 (复用)
- **MOSI**: GPIO1 (LCD命令/SD卡数据)
- **MISO**: GPIO42 (仅SD卡)
- **SCLK**: GPIO2 (LCD命令/SD卡时钟)
- **LCD_CS**: EXIO3 (GPIO扩展芯片)
- **SD_CS**: EXIO4 (GPIO扩展芯片)

#### LCD RGB接口
- **PCLK**: GPIO41 (像素时钟)
- **DE**: GPIO40 (数据使能)
- **VSYNC**: GPIO39 (垂直同步)
- **HSYNC**: GPIO38 (水平同步)
- **RGB数据线**: GPIO5,45,48,47,21,14,13,12,11,10,9,46,3,8,18,17
- **背光**: GPIO6 (PWM控制)

#### I2S音频接口
- **MCK**: GPIO35 (主时钟)
- **BCK**: GPIO36 (位时钟)
- **WS**: GPIO37 (字选择)
- **DI**: GPIO19 (ES7210输入)
- **DO**: GPIO20 (ES8311输出)

#### GPIO扩展芯片 (TCA9554PWR)
- **EXIO0**: 蜂鸣器控制 ⚠️
- **EXIO1**: LCD_RST (LCD复位)
- **EXIO2**: TOUCH_RST (触摸屏复位)
- **EXIO3**: LCD_CS (LCD片选)
- **EXIO4**: SD_CS (SD卡片选)
- **EXIO5**: QMI8658_INT2 (姿态传感器中断2)
- **EXIO6**: QMI8658_INT1 (姿态传感器中断1)
- **EXIO7**: RTC_INT (RTC中断)

#### 其他引脚
- **触摸中断**: GPIO16
- **GPIO扩展中断**: GPIO0
- **电池电压检测**: GPIO4 (ADC)

### 底座设备引脚分配
- **声音传感器**: GPIO0 (ADC)
- **WS2812灯带**: GPIO8 (30颗LED)
- **用户按键**: GPIO9
- **状态指示灯**: GPIO18

## 三、引脚冲突解决方案

### ⚠️ 重要提醒：蜂鸣器控制引脚冲突
**问题**: 原设计文档中蜂鸣器控制为EXIO8，但TCA9554PWR只有8个引脚(EXIO0-EXIO7)

**解决方案**:
1. **推荐方案**: 使用EXIO0控制蜂鸣器
2. **备选方案**: 使用ESP32-S3的其他可用GPIO直接控制蜂鸣器

**当前实现**: 代码中已将蜂鸣器分配到EXIO0

### I2C总线共享
- 通过设备地址区分不同设备
- 实现互斥访问控制
- 支持设备在线检测

### SPI接口复用
- LCD和SD卡通过片选信号区分
- LCD主要使用RGB并行接口，SPI仅用于初始化
- 实现时分复用访问

## 四、软件编译和烧录

### 环境要求
- ESP-IDF v5.0 或更高版本
- Python 3.8+
- Git

### 编译步骤

#### 主体设备 (ESP32-S3)
```bash
cd esp_idf
idf.py set-target esp32s3
idf.py menuconfig  # 配置项目参数
idf.py build
idf.py flash monitor
```

#### 底座设备 (ESP32-C2)
```bash
cd esp_idf/base_station
idf.py set-target esp32c2
idf.py menuconfig
idf.py build
idf.py flash monitor
```

### 重要配置项
1. **分区表**: 确保Flash分区足够大
2. **PSRAM**: 启用PSRAM支持 (仅ESP32-S3)
3. **WiFi**: 配置WiFi参数
4. **蓝牙**: 启用蓝牙功能

## 五、系统启动流程

### 主体设备启动
1. 系统初始化 (NVS、网络、电源管理)
2. I2C总线初始化
3. GPIO扩展芯片初始化
4. SPI总线初始化
5. 传感器初始化
6. LCD显示屏初始化
7. 触摸屏初始化
8. I2S音频接口初始化
9. 音频系统初始化
10. SD卡初始化 (可选)

### 底座设备启动
1. 系统初始化
2. WS2812灯带初始化
3. 声音传感器初始化
4. 蓝牙通信初始化
5. 氛围灯效系统启动

### 设备配对流程
1. 底座设备进入配对模式
2. 主体设备扫描蓝牙设备
3. 自动配对连接
4. 建立通信协议

## 六、功能使用指南

### 基本操作
- **开机**: 长按电源键3秒
- **关机**: 长按电源键5秒
- **重启**: 同时按住电源键和复位键

### 触摸操作
- **点击**: 单次触摸
- **长按**: 触摸保持2秒
- **滑动**: 手指在屏幕上滑动
- **双击**: 快速连续两次点击

### 语音交互
- **唤醒词**: "小TIMO" 或自定义唤醒词
- **语音命令示例**:
  - "设置明天早上7点的闹钟"
  - "播放音乐"
  - "查看今天的天气"
  - "开始番茄时钟"

### 氛围灯效
- **待机模式**: 柔和呼吸灯
- **对话模式**: 音乐可视化效果
- **闹钟模式**: 日出模拟灯效
- **预警模式**: 分级颜色警示

## 七、故障排除

### 常见问题

#### 1. 设备无法启动
**症状**: 屏幕无显示，无任何反应
**可能原因**:
- 电池电量不足
- 硬件连接问题
- 固件损坏

**解决方法**:
1. 连接充电器充电30分钟
2. 检查硬件连接
3. 重新烧录固件

#### 2. 触摸屏无响应
**症状**: 屏幕显示正常，但触摸无效
**可能原因**:
- 触摸屏驱动未正确初始化
- I2C通信异常
- 触摸屏硬件故障

**解决方法**:
1. 重启设备
2. 检查I2C连接
3. 查看串口日志确认触摸屏初始化状态

#### 3. 传感器数据异常
**症状**: 传感器读数明显错误或无数据
**可能原因**:
- 传感器未正确初始化
- I2C地址冲突
- 传感器硬件故障

**解决方法**:
1. 重启设备
2. 检查I2C设备扫描结果
3. 验证传感器硬件连接

#### 4. 蓝牙连接失败
**症状**: 主体和底座无法配对
**可能原因**:
- 蓝牙未正确初始化
- 设备距离过远
- 蓝牙协议不兼容

**解决方法**:
1. 确保两设备距离在1米内
2. 重启两个设备
3. 手动触发重新配对

#### 5. SD卡无法识别
**症状**: SD卡插入后无法挂载
**可能原因**:
- SD卡格式不支持
- SPI接口问题
- SD卡硬件故障

**解决方法**:
1. 使用FAT32格式的SD卡
2. 检查SPI连接
3. 尝试其他SD卡

### 调试方法

#### 串口日志
- 波特率: 115200
- 数据位: 8
- 停止位: 1
- 校验位: 无

#### 日志级别
- ERROR: 错误信息
- WARN: 警告信息
- INFO: 一般信息
- DEBUG: 调试信息

#### 关键日志标签
- `TIMO_MAIN`: 主程序
- `HARDWARE_INIT`: 硬件初始化
- `I2C_BUS`: I2C总线
- `GPIO_EXPANDER`: GPIO扩展
- `LCD_DRIVER`: LCD驱动
- `SENSORS`: 传感器

## 八、维护和更新

### 固件更新
1. 下载最新固件
2. 连接设备到电脑
3. 使用ESP-IDF工具烧录
4. 重启设备验证

### 配置备份
- 系统配置存储在NVS分区
- 可通过串口命令导出配置
- 建议定期备份重要配置

### 清理维护
- 定期清洁屏幕和外壳
- 避免在高温高湿环境使用
- 保持充电接口清洁

## 九、API接口说明

### 硬件抽象层API

#### I2C总线接口
```c
// 初始化I2C总线
esp_err_t i2c_bus_init(void);

// 写入数据
esp_err_t i2c_bus_write_bytes(uint8_t device_addr, uint8_t reg_addr, uint8_t *data, size_t len);

// 读取数据
esp_err_t i2c_bus_read_bytes(uint8_t device_addr, uint8_t reg_addr, uint8_t *data, size_t len);

// 检查设备在线状态
bool i2c_bus_device_online(uint8_t device_addr);
```

#### 传感器接口
```c
// 读取温湿度
esp_err_t aht30_read(aht30_data_t *data);

// 读取光照强度
esp_err_t bh1750_read(bh1750_data_t *data);

// 读取CO2浓度
esp_err_t sgp30_read(sgp30_data_t *data);

// 读取姿态数据
esp_err_t qmi8658_read(qmi8658_data_t *data);
```

#### LCD显示接口
```c
// 初始化LCD
esp_err_t lcd_init(void);

// 设置背光亮度
esp_err_t lcd_set_backlight(uint8_t brightness);

// 绘制位图
esp_err_t lcd_draw_bitmap(int x, int y, int width, int height, uint16_t *color_data);

// 填充屏幕
esp_err_t lcd_fill_screen(uint16_t color);
```

#### 触摸屏接口
```c
// 初始化触摸屏
esp_err_t touch_init(void);

// 读取触摸点
esp_err_t touch_read_point(touch_point_t *point);
```

#### GPIO扩展接口
```c
// 初始化GPIO扩展芯片
esp_err_t gpio_expander_init(void);

// 设置扩展引脚电平
esp_err_t gpio_expander_set_level(exio_pin_t pin, uint8_t level);

// 读取扩展引脚电平
esp_err_t gpio_expander_get_level(exio_pin_t pin, uint8_t *level);

// 便捷宏定义
#define LCD_RST_HIGH()      gpio_expander_set_level(EXIO1, 1)
#define LCD_CS_LOW()        gpio_expander_set_level(EXIO3, 0)
#define SD_CS_LOW()         gpio_expander_set_level(EXIO4, 0)
#define BUZZER_ON()         gpio_expander_set_level(EXIO0, 1)
```

### 数据结构定义

#### 传感器数据结构
```c
// 温湿度数据
typedef struct {
    float temperature;  // 温度 (°C)
    float humidity;     // 湿度 (%)
} aht30_data_t;

// 光照数据
typedef struct {
    float lux;          // 光照强度 (lux)
} bh1750_data_t;

// CO2数据
typedef struct {
    uint16_t co2;       // CO2浓度 (ppm)
    uint16_t tvoc;      // TVOC浓度 (ppb)
} sgp30_data_t;

// 姿态数据
typedef struct {
    float acc_x, acc_y, acc_z;      // 加速度 (g)
    float gyro_x, gyro_y, gyro_z;   // 角速度 (dps)
} qmi8658_data_t;

// 触摸点数据
typedef struct {
    int x;              // X坐标
    int y;              // Y坐标
    bool pressed;       // 是否按下
} touch_point_t;

// RTC时间数据
typedef struct {
    uint8_t year;       // 年 (0-99, 表示2000-2099)
    uint8_t month;      // 月 (1-12)
    uint8_t day;        // 日 (1-31)
    uint8_t hour;       // 时 (0-23)
    uint8_t minute;     // 分 (0-59)
    uint8_t second;     // 秒 (0-59)
    uint8_t weekday;    // 星期 (0-6, 0=星期日)
} rtc_time_t;
```

## 十、性能参数

### 系统性能
- **CPU频率**: 240MHz (ESP32-S3) / 120MHz (ESP32-C2)
- **内存**: 512KB SRAM + 8MB PSRAM (ESP32-S3) / 272KB SRAM (ESP32-C2)
- **Flash**: 16MB (ESP32-S3) / 2MB (ESP32-C2)
- **启动时间**: < 3秒
- **功耗**: 待机 < 100mA, 工作 < 500mA

### 显示性能
- **分辨率**: 480×480像素
- **色深**: RGB565 (16位)
- **刷新率**: 60Hz
- **亮度**: 0-100% PWM调节
- **响应时间**: < 50ms

### 传感器精度
- **温度**: ±0.3°C (AHT30)
- **湿度**: ±2% RH (AHT30)
- **光照**: 1-65535 lux (BH1750)
- **CO2**: 400-60000 ppm (SGP30)
- **加速度**: ±4g (QMI8658)
- **角速度**: ±512dps (QMI8658)

### 通信性能
- **I2C速度**: 400kHz
- **SPI速度**: 10MHz
- **蓝牙范围**: 10米
- **WiFi标准**: 802.11 b/g/n

## 十一、安全注意事项

### 电气安全
- 使用原装充电器
- 避免短路和过载
- 不要在潮湿环境中使用

### 数据安全
- 定期备份重要数据
- 使用安全的WiFi网络
- 及时更新固件

### 使用安全
- 避免强烈撞击
- 不要拆解设备
- 远离高温热源

## 十二、技术支持

### 开发者信息
- 项目名称: TIMO智能闹钟
- 版本: 1.0.0
- 开发日期: 2025-06-27
- 开发框架: ESP-IDF v5.0+

### 文档版本
- 用户手册版本: 1.0.0
- 最后更新: 2025-06-27
- 适用固件版本: 1.0.0

### 联系方式
- 技术支持: <EMAIL>
- 用户手册: https://docs.timo-clock.com
- 源代码: https://github.com/timo-clock/firmware
- 问题反馈: https://github.com/timo-clock/firmware/issues

### 许可证
本项目遵循 MIT 开源许可证，详见 LICENSE 文件。

---

**注意**: 本文档基于当前软件版本编写，后续版本可能会有功能变更。请以最新版本文档为准。
