# 智能闹钟TIMO设计

## 一、功能需求

### （一）硬件设计规格

#### 1.主体设备（半球形智能终端）
- **外形**: 半球形设计
- **显示屏**: 480×480像素圆形LCD触摸屏
- **主控芯片**: ESP32-S3N16R8（16MB Flash + 8MB PSRAM）
- **音频系统**: 3W 4Ω单声道扬声器 + 双MEMS麦克风阵列
- **传感器模块**: 
  - QST姿态传感器（用于手势和姿态检测）
  - 光照传感器（自动背光调节）
  - 温湿度传感器
  - CO2浓度传感器
- **存储扩展**: 支持最大32GB TF卡
- **电源系统**: 2000mAh锂电池 + 10W无线充电接收
- **通信**: 蓝牙5.0与底座通信

#### 2.底座设备（充电和氛围灯控制）
- **接口**: USB Type-C电源输入
- **无线充电**: 15W无线充电发射端
- **主控芯片**: ESP32-C2（2MB Flash）
- **灯光系统**: WS2812 RGB灯带（30颗LED）
- **通信**: 蓝牙5.0与主体通信
- **传感器模块**: 声音传感器

### （二）软件功能规格

#### 1. 主体功能

##### （1）系统架构要求
- **开发框架**: ESP-IDF + ESP-Brookesia + LVGL
- **核心功能**: 全双工语音交互、多模态识别、智能体控制

##### （2）语音交互系统
- **语音唤醒**: 支持自定义唤醒词
- **对话功能**: 连续对话、对话打断、声纹识别
- **AI集成**: 接入大语言模型，支持自然语言处理
- **语音指令**: 时间查询、天气预报、待办管理、闹钟控制、氛围场景切换
- **声音个性化**: 声音复刻功能

##### （3）时间管理功能
- **时间显示**: 自动网络校时，支持手动时间配置
- **多闹钟系统**: 
  - 支持多组闹钟设置（手动/语音）
  - 闹钟自定义名称和提醒内容
  - 可配置专属氛围场景
  - 语音播放提醒内容

##### （4）任务管理系统
- **待办事项**: 
  - 多条待办创建（手动/语音）
  - 提醒氛围场景配置
  - 延迟/完成状态管理
  - 历史记录统计
  - 同步云端，本地不存储

##### （5）环境监测与预警
- **传感器阈值配置**: CO2、温度、湿度报警值设定
- **多重报警**: 声光报警 + 微信推送通知
- **氛围场景**: 可配置预警专属灯光效果

##### （6）专注力管理--番茄时钟
- **多端控制**: 屏幕操作、小程序、语音控制
- **数据分析**: 实时专注力评估

##### （7）虚拟宠物系统
- **生命周期**: 领养、成长、陪伴
- **交互方式**: 聊天、触摸、姿态感应
- **情感系统**: 识别主人情绪，表达自己的情绪（灯光、声音、表情）

##### （8）氛围场景系统（声音为主体提供，灯光为底座提供）
- **对话律动**: 底座执行对话氛围灯效，屏幕显示与宠物对话的 内容以及宠物表情
- **音乐律动**: 屏幕显示音乐频谱、底座执行音乐氛围灯效
- **晨间唤醒**: 屏幕亮度渐变，显示当日待办事项、底座执行日出模拟灯效、主体渐变音量播放配置的唤醒音效或音乐
- **小憩唤醒**: 轻柔渐变提醒、可配置铃声
- **助眠模式**: 暖光呼吸、屏幕变暗显示动态睡眠表情、白噪音播放（海浪/壁炉/森林/雨声）
- **待办提醒**: 专属音效、语音内容播放
- **事件预警**: 分级声光报警（根据危险等级调节音量）
- **专注模式**: 显示、灯光、声音协调配合

##### （9）用户界面系统
- **UI框架**: LVGL图形库
- **屏幕规格**: 480×480圆形触摸屏适配
- **主题系统**: 
  - 表盘式主题切换
  - 多套预置主题
  - 默认卡通风格主题
  - 支持通过小程序下载主题
- **自适应背光**: 光感器自动调节

##### （10）音频资源管理
- **内置音效**: 系统提示音、场景音效
- **用户音乐**: 支持TF卡音乐文件播放
- **音频格式**: 支持常见音频格式

##### （11）蓝牙配对系统
- **自动配对流程**: 
  - 主体开机时自动开启蓝牙，若已有配对底座信息，则尝试连接底座，连接成功则显示已连接的蓝牙图标，若连接失败，则关闭蓝牙，显示未连接的蓝牙图标。若不存在配对底座信息，则开启蓝牙搜索
  - 主体显示配对确认界面
  - 用户确认完成配对

- **配对管理**: 支持删除和重新配对
- **故障处理**: 配对失败时手动扫描功能


#### 2. 底座功能

##### （1）系统架构要求
- **开发框架**: ESP-IDF
- **核心功能**: 与主体通过蓝牙进行通信，接收主体指令，执行相应功能

##### （2）蓝牙配对系统
- **连接检测**: 底座定期检测已连接的主体状态，若是未连接主体，则检测是否存在已配对的主体，若存在则开启蓝牙等待主体连接
- **配对请求**: 若不存在已配对的主体，则发送蓝牙配对请求
- **手动配对**: 用户主动按配对按键，则发送蓝牙配对请求

##### （3）氛围场景系统
- **场景切换**：接收主体场景切换指令
- 内置氛围场景：
  - **对话律动**: 对话氛围灯效，从中心向两侧扩散，中心最亮，向边缘逐渐变暗
  - **音乐律动**: 音乐可视化，3种模式
    - **基础律动模式**：根据声音传感器检测音量，实时变化灯光颜色和亮度
    - **波浪律动模式**：在音乐律动基础上增加波浪传播效果
    - **频谱律动模式**：在一条灯带上显示频谱灯效，低中高频分别红色→橙色→黄色→绿色→蓝色的平滑过渡，最后一颗LED明显滞后于主体，形成独特的拖尾效果
  - **晨间唤醒**: 日出模拟灯效，3分钟完整日出模拟，从黑、蓝、橙、黄、红、白色的渐变色彩变化
  - **助眠模式**: 呼吸灯效，色彩模式：下雨--天空蓝、海浪--深蓝、火焰--火红、森林--草绿
  - **事件预警**: 分级灯光闪烁报警（低：黄色、中：橙色、高：红色）



#### 3. 云端服务
- **设备服务**: 设备注册管理、MQTT服务
- **语音对话服务**: ASR、TTS、声纹识别、声音复刻、声音打断、推理大模型、角色配置服务
- **MCP服务**: 时间查询、天气预报、待办事项、闹钟设置与查询、氛围场景切换
- **待办事项服务**：为主体及小程序提供待办事项服务
- **主题设计工具接口**: 在线表盘主题设计器
- **主题社区功能接口**: 主题上架、点赞、收藏
- **主题资源分享接口**: 主题下载

#### 4. 微信小程序
- **网络配置**: 主体WiFi配网功能
- **状态监控**: 主体、底座状态及传感器数据实时显示
- **远程控制**: 设置所有主体配置项
- **场景管理**: 氛围场景配置，灯光、声效管理
- **宠物管理**: 在线注册、删除宠物、修改宠物信息、查看宠物状态
- **待办事项**: 待办事项管理
- **主题设计工具**: 在线表盘主题设计器
- **主题社区功能**: 主题上架、点赞、收藏
- **主题资源分享**: 主题下载和使用


---

## 二、主体硬件资源
1、主控：ESP32-S3N16R8
2、TFT屏幕圆形屏
 - 屏幕分辨率：480*480
 - 控制芯片：ST7701
 - Touch ：GT911

3、传感器
 - QST姿态传感器QMI8658
 - 温湿度传感器AHT30
 - 光照传感器BH1750
 - CO2传感器SGP30

4、GPIO 扩展芯片TCA9554PWR
5、RESET 按键、开关机按键
6、RTC 时钟芯片 PCF85063
7、蜂鸣器
8、双MEMS麦克风阵列
9、音频输入 ES7210
10、音频输出 ES8311
11、Micro SD
12、功放芯片NS4150B
13、3W 4Ω单声道扬声器



## 三、主体内部硬件连接

### LCD
- LCD 接口（8 位 + 16 位并行 RGB、 I8080、 MOTO6800） , 支持 RGB565、 YUV422、 YUV420、 YUV411之间互相转换
- LCD TOUCH地址:0x5D
- LCD 引脚29。其中RGB565:16 + 4，TP: 4，LCD命令SPI：4，LCD背光：1

| LCD引脚 | ESP32S3          |
| :------ | :--------------- |
| LCD_BL  | GPIO6            |
| LCD_RST | EXIO1            |
| LCD_SDA | GPIO1            |
| LCD_SCL | GPIO2            |
| LCD_CS  | EXIO3            |
| PCLK    | GPIO41           |
| DE      | GPIO40           |
| VSYNC   | GPIO39           |
| HSYNC   | GPIO38           |
| B0      | NC               |
| B1      | GPIO5            |
| B2      | GPIO45           |
| B3      | GPIO48           |
| B4      | GPIO47           |
| B5      | GPIO21           |
| G0      | GPIO14           |
| G1      | GPIO13           |
| G2      | GPIO12           |
| G3      | GPIO11           |
| G4      | GPIO10           |
| G5      | GPIO9            |
| R0      | NC               |
| R1      | GPIO46           |
| R2      | GPIO3            |
| R3      | GPIO8            |
| R4      | GPIO18           |
| R5      | GPIO17           |
| TP_SDA  | GPIO15           |
| TP_SCL  | GPIO7            |
| TP_INT  | GPIO16           |
| TP_RST  | EXIO2            |

### SD Card
| SD Card       | ESP32S3 |
| :------------ | :------ |
| SD_D0 / MISO  | GPIO42  |
| SD_CMD / MOSI | GPIO1   |
| SD_SCK / SCLK | GPIO2   |
| SD_D3 / CS    | EXIO4   |
| SD_D1         | NC      |
| SD_D2         | NC      |

### QMI (地址:0x6A)
| QMI8658  | ESP32S3 |
| :------- | :------ |
| IMU_SCL  | GPIO7   |
| IMU_SDA  | GPIO15  |
| IMU_INT1 | EXIO6   |
| IMU_INT2 | EXIO5   |

### RTC (地址:0x51)
| PCF85063ATL | ESP32S3 |
| :---------- | :------ |
| RTC_SCL     | GPIO7   |
| RTC_SDA     | GPIO15  |
| RTC_INT     | EXIO7   |

### TCA9554PWR (地址:0x20)
| TCA9554PWR  | ESP32S3 |
| :---------- | :------ |
| EXIO_SCL    | GPIO7   |
| EXIO_SDA    | GPIO15  |
| EXIO_INT    | GPIO0   |

### AHT30 (地址:0x38)
| AHT30     | ESP32S3 |
| :-------- | :------ |
| AHT30_SCL | GPIO7   |
| AHT30_SDA | GPIO15  |

### BH1750 (地址:0x23)
| BH1750     | ESP32S3 |
| :--------- | :------ |
| BH1750_SCL | GPIO7   |
| BH1750_SDA | GPIO15  |

### SGP30 (地址:0x58)
| SGP30     | ESP32S3 |
| :-------- | :------ |
| SGP30_SCL | GPIO7   |
| SGP30_SDA | GPIO15  |

### ES7210 (地址:0x41)
| ES7210     | ESP32S3 |
| :--------- | :------ |
| ES7210_SCL | GPIO7   |
| ES7210_SDA | GPIO15  |
| ES7210_MCK | GPIO35  |
| ES7210_BCK | GPIO36  |
| ES7210_WS  | GPIO37  |
| ES7210_DI  | GPIO19  |

### ES8311 (地址:0x18)
| ES8311     | ESP32S3 |
| :--------- | :------ |
| ES8311_SCL | GPIO7   |
| ES8311_SDA | GPIO15  |
| ES8311_MCK | GPIO35  |
| ES8311_BCK | GPIO36  |
| ES8311_WS  | GPIO37  |
| ES8311_DO  | GPIO20  |

### Buzzer
| Buzzer         | ESP32S3 |
| :------------- | :------ |
| Buzzer_Control | EXIO8   |

### BAT
| BAT     | ESP32S3 |
| :------ | :------ |
| BAT_ADC | GPIO4   |

---
## 四、底座硬件资源
1、主控：ESP32-C2(ESP8684H2)
2、传感器
 - 声音传感器

3、WS2812灯带（30颗LED）
4、LED连接指示灯
5、用户按键


## 五、底座内部硬件连接

| 外设           | ESP32C2 |
| :------------- | :------ |
| 声音传感器AO脚  | 0       |
| RGB彩灯WS       | 8       |
| 用户按键        | 9       |
| LED连接指示灯    | 18      |

---

## 六、附录
### 1、ESP32-S3产品特性
#### Wi-Fi
- ◇ 支持 IEEE 802.11b/g/n 协议
- ◇ 在 2.4 GHz 频带支持 20 MHz 和 40 MHz 频宽
- ◇ 支持 1T1R 模式，数据速率高达 150 Mbps
- ◇ 无线多媒体 (WMM)
- ◇ 帧聚合 (TX/RX A-MPDU, TX/RX A-MSDU)
- ◇ 立即块确认 (Immediate Block ACK)
- ◇ 分片和重组 (Fragmentation/defragmentation)
- ◇ Beacon 自动监测（硬件 TSF）
- ◇ 4 个虚拟 Wi-Fi 接口
- ◇ 同时支持基础结构型网络 (Infrastructure BSS) Station 模式、 SoftAP 模式和 Station + SoftAP 模式
请注意， ESP32-S3 在 Station 模式下扫描时， SoftAP 信道会同时改变
- ◇ 天线分集
- ◇ 802.11 mc FTM
#### 蓝牙
- ◇ 低功耗蓝牙 (Bluetooth LE)： Bluetooth 5、 Bluetooth mesh
- ◇ 高功率模式 (20 dBm)
- ◇ 速率支持 125 Kbps、 500 Kbps、 1 Mbps、 2 Mbps
- ◇ 广播扩展 (Advertising Extensions)
- ◇ 多广播 (Multiple Advertisement Sets)
- ◇ 信道选择 (Channel Selection Algorithm #2)
- ◇ Wi-Fi 与蓝牙共存，共用同一个天线
#### CPU 和存储
- ◇ Xtensa® 32 位 LX7 双核处理器
- ◇ 时钟频率：最高 240 MHz
- ◇ CoreMark® 得分：
  - 双核，主频 240 MHz： 1329.92 CoreMark； 5.54 CoreMark/MHz
- ◇ 五级流水线架构
- ◇ 128 位数据总线位宽，专用的 SIMD 指令
- ◇ 单精度浮点运算单元 (FPU)
- ◇ L1 cache
- ◇ ROM： 384 KB
- ◇ SRAM： 512 KB
- ◇ RTC SRAM： 16 KB
- ◇ 支持 SPI 协议： SPI、 Dual SPI、 Quad SPI、 Octal SPI、 QPI、 OPI，可外接 flash、片外 RAM 和其他 SPI 设备
- ◇ 引入 cache 机制的 flash 控制器
- ◇ 支持 flash 在线编程

#### 高级外设接口和传感器
- ◇ 45 个可编程 GPIO
  - 4 个作为 strapping 管脚
  - 6 个或 7 个用于连接封装内 flash 或 PSRAM：
    * ESP32-S3FN8、 ESP32-S3R2、 ESP32-S3R8、 ESP32-S3R8V、 ESP32-S3R16V： 6 个用于连接
    * ESP32-S3FH4R2： 7 个用于连接
 
- ◇ 数字接口：
  - 2 个 SPI 接口用于连接 flash 和 RAM
  - 2 个通用 SPI 接口
  - LCD 接口（8 位 + 16 位并行 RGB、 I8080、 MOTO6800） , 支持 RGB565、 YUV422、 YUV420、 YUV411
  之间互相转换
  - DVP 8 位 + 16 位摄像头接口
  - 3 个 UART 接口
  - 2 个 I2C 接口
  - 2 个 I2S 接口
  - RMT (TX/RX)
  - 脉冲计数器
  - LED PWM 控制器，多达 8 个通道
  - 全速 USB OTG
  - USB 串口/JTAG 控制器
  - 2 个电机控制脉宽调制器 (MCPWM)
  - SD/MMC 主机接口，具有 2 个卡槽
  - 通用 DMA 控制器 (简称 GDMA)， 5 个接收通道和 5 个发送通道
  - TWAI® 控制器，兼容 ISO 11898-1（CAN 规范 2.0）
  - 片上 JTAG 调试功能

- ◇ 模拟接口：
  - 2 个 12 位 SAR ADC，多达 20 个通道
  - 温度传感器
  - 14 个电容式传感 GPIO
- ◇ 定时器：
  - 4 个 54 位通用定时器
  - 52 位系统定时器
  - 3 个看门狗定时器
#### 功耗管理
- ◇ 通过选择时钟频率、占空比、 Wi-Fi 工作模式和单独控制内部器件的电源，实现精准电源控制
- ◇ 针对典型场景设计的四种功耗模式： Active、 Modem-sleep、 Light-sleep、 Deep-sleep
- ◇ Deep-sleep 模式下功耗低至 7 µA
- ◇ 超低功耗协处理器 (ULP)：
  - ULP-RISC-V 协处理器
  - ULP-FSM 协处理器
- ◇ Deep-sleep 模式下 RTC 存储器仍保持工作
#### 安全机制
- ◇ 安全启动
- ◇ Flash 加密
- ◇ 4-Kbit OTP，用户可用的高达 1792 位
- ◇ 加密硬件加速器：
  - AES-128/256 (FIPS PUB 197)
  - SHA (FIPS PUB 180-4)
  - RSA
  - 随机数生成器 (RNG)
  - HMAC
  - 数字签名

### 2、ESP32-C2(ESP8684)产品特性
#### Wi-Fi
- ◇ 支持 IEEE 802.11b/g/n 协议
- ◇ 在 2.4 GHz 频带支持 20 MHz 频宽
- ◇ 支持 1T1R 模式，数据速率高达 72.2 Mbps
- ◇ 无线多媒体 (WMM)
- ◇ 帧聚合 (TX/RX A-MPDU, TX/RX A-MSDU)
- ◇ 立即块确认 (Immediate Block ACK)
- ◇ 分片和重组 (Fragmentation and defragmentation)
- ◇ 传输机会 (Transmit opportunity, TXOP)
- ◇ Beacon 自动监测（硬件 TSF）
- ◇ 3 个虚拟 Wi-Fi 接口
- ◇ 同时支持基础结构型网络 (Infrastructure BSS) Station 模式、 SoftAP 模式、 Station + SoftAP 模式和混杂模式
请注意 ESP8684 系列在 Station 模式下扫描时， SoftAP 信道会同时改变
- ◇ 天线分集

#### 蓝牙
- ◇ 低功耗蓝牙 (Bluetooth LE)：通过 Bluetooth 5.3 认证
- ◇ 高功率模式 (20 dBm)
- ◇ 速率支持 125 Kbps、 500 Kbps、 1 Mbps、 2 Mbps
- ◇ 广播扩展 (Advertising Extensions)
- ◇ 多广播 (Multiple Advertisement Sets)
- ◇ 信道选择 (Channel Selection Algorithm #2)
- ◇ Wi-Fi 与蓝牙共存，共用同一个天线

#### CPU 和存储
- ◇ RISC-V 32 位单核处理器，主频高达 120 MHz
- ◇ CoreMark® 分数：
- 单核 120 MHz： 305.42 CoreMark； 2.55 CoreMark/MHz
- ◇ 576 KB ROM
- ◇ 272 KB SRAM（其中 16 KB 专用于 cache）
- ◇ 封装内 flash（不同型号有差异，详见章节 1 ESP8684 系列型号对比）
- ◇ 引入 cache 机制的 flash 控制器
- ◇ 支持 flash 在线编程 (ICP)

#### 高级外设接口
- ◇ 14 个 GPIO
- 2 个作为 strapping 管脚
- ◇ 数字接口：
- 3 个 SPI
- 2 个 UART
- I2C 主机
- LED PWM 控制器，多达 6 个通道
- 通用 DMA 控制器（简称 GDMA）， 1 个接收通道和 1 个发送通道
- ◇ 模拟接口：
- 12 位 SAR 模/数转换器，多达 5 个通道
- 温度传感器
- ◇ 定时器：
- 54 位通用定时器
- 2 个看门狗定时器
- 52 位系统定时器

#### 低功耗管理
- ◇ 通过选择时钟频率、占空比、 Wi-Fi 工作模式和单独控制内部器件的电源，实现精准电源控制
- ◇ 针对典型场景设计的四种功耗模式： Active、 Modem-sleep、 Light-sleep、 Deep-sleep
- ◇ Deep-sleep 模式下功耗低至 5 μA
- ◇ Deep-sleep 模式下 RTC 存储器仍保持工作

#### 安全机制
- ◇ 安全启动 - 内部和外部存储器的权限控制
- ◇ flash 加密 - 加密和解密存储器
- ◇ 1024 位 OTP，用户可用的为 256 位
- ◇ 加密硬件加速器：
- ECC
- SHA 加速器 (FIPS PUB 180-4)
- ◇ 随机数生成器 (RNG)
- ◇ 时钟毛刺过滤器

#### RF 模块
- ◇ 天线开关、射频巴伦 (balun)、功率放大器、低噪声放大器
- ◇ 802.11b 传输功率高达 +22 dBm
- ◇ 802.11g 传输功率高达 +20 dBm
- ◇ 低功耗蓝牙接收器灵敏度 (125 Kbps) 高达 -106 dBm
