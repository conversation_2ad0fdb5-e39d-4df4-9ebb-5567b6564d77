#!/bin/bash

# TIMO底座固件编译测试脚本

echo "=== TIMO底座固件编译测试 ==="

# 检查ESP-IDF环境
if [ -z "$IDF_PATH" ]; then
    echo "错误: ESP-IDF环境未设置"
    echo "请运行: . \$HOME/esp/esp-idf/export.sh"
    exit 1
fi

echo "ESP-IDF路径: $IDF_PATH"

# 设置目标芯片
echo "设置目标芯片为ESP32-C2..."
idf.py set-target esp32c2

# 清理之前的构建
echo "清理之前的构建..."
idf.py fullclean

# 开始编译
echo "开始编译..."
idf.py build

# 检查编译结果
if [ $? -eq 0 ]; then
    echo "✅ 编译成功!"
    echo ""
    echo "固件信息:"
    echo "- 项目名称: TIMO底座固件"
    echo "- 目标芯片: ESP32-C2"
    echo "- 版本: v1.0.0"
    echo ""
    echo "生成的文件:"
    ls -la build/*.bin 2>/dev/null || echo "  未找到.bin文件"
    ls -la build/*.elf 2>/dev/null || echo "  未找到.elf文件"
    echo ""
    echo "烧录命令:"
    echo "  idf.py -p /dev/ttyUSB0 flash monitor"
else
    echo "❌ 编译失败!"
    echo "请检查错误信息并修复问题"
    exit 1
fi
