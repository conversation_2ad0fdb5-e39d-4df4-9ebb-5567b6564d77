/**
 * @file base_main.h
 * @brief TIMO底座设备主程序头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef BASE_MAIN_H
#define BASE_MAIN_H

#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "base_config.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 系统事件组 */
extern EventGroupHandle_t g_base_event_group;

/* 消息队列 */
extern QueueHandle_t g_led_command_queue;
extern QueueHandle_t g_bluetooth_data_queue;

/* 系统事件位定义 */
#define BASE_EVENT_SYSTEM_READY     BIT0
#define BASE_EVENT_BLE_READY        BIT1
#define BASE_EVENT_LED_READY        BIT2
#define BASE_EVENT_SOUND_READY      BIT3
#define BASE_EVENT_BUTTON_READY     BIT4
#define BASE_EVENT_BLE_CONNECTED    BIT5
#define BASE_EVENT_BLE_DISCONNECTED BIT6
#define BASE_EVENT_SOUND_DETECTED   BIT7
#define BASE_EVENT_BUTTON_PRESS     BIT8
#define BASE_EVENT_BUTTON_LONG      BIT9

/* LED命令结构体 */
typedef struct {
    ambient_scene_t scene;
    rgb_color_t color;
    uint8_t brightness;
    uint16_t speed;
    uint32_t duration_ms;
} led_command_t;

/* 蓝牙数据结构体 */
typedef struct {
    uint8_t type;
    uint8_t length;
    uint8_t data[64];
} bluetooth_data_t;

/* 声音事件结构体 */
typedef struct {
    uint16_t level;
    uint32_t timestamp;
} sound_event_t;

/* 按键事件结构体 */
typedef struct {
    bool pressed;
    bool long_press;
    uint32_t timestamp;
} button_event_t;

/**
 * @brief 启动底座应用程序
 * @return esp_err_t 
 */
esp_err_t base_main_start(void);

/**
 * @brief 停止底座应用程序
 * @return esp_err_t 
 */
esp_err_t base_main_stop(void);

/**
 * @brief 获取系统运行时间
 * @return uint64_t 运行时间(毫秒)
 */
uint64_t base_get_uptime_ms(void);

/**
 * @brief 系统重启
 */
void base_system_restart(void);

/**
 * @brief 进入深度睡眠
 * @param sleep_time_us 睡眠时间(微秒)
 */
void base_enter_deep_sleep(uint64_t sleep_time_us);

#ifdef __cplusplus
}
#endif

#endif /* BASE_MAIN_H */
