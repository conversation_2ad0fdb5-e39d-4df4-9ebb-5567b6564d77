/**
 * @file time_manager.h
 * @brief TIMO时间管理器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef TIME_MANAGER_H
#define TIME_MANAGER_H

#include "esp_err.h"
#include "hardware_hal.h"
#include <time.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 时间源定义 */
typedef enum {
    TIME_SOURCE_NONE = 0,       // 无时间源
    TIME_SOURCE_RTC,            // RTC时钟
    TIME_SOURCE_NTP,            // 网络时间
    TIME_SOURCE_MANUAL,         // 手动设置
    TIME_SOURCE_MAX
} time_source_t;

/* 时间状态定义 */
typedef enum {
    TIME_STATUS_UNSET = 0,      // 时间未设置
    TIME_STATUS_RTC_ONLY,       // 仅RTC时间
    TIME_STATUS_SYNCED,         // 已同步
    TIME_STATUS_DRIFT,          // 时间漂移
    TIME_STATUS_ERROR,          // 时间错误
    TIME_STATUS_MAX
} time_status_t;

/* 时间信息结构体 */
typedef struct {
    time_t timestamp;           // Unix时间戳
    struct tm timeinfo;         // 时间结构
    time_source_t source;       // 时间源
    time_status_t status;       // 时间状态
    int64_t last_sync_time;     // 上次同步时间
    int32_t drift_ms;           // 时间漂移(毫秒)
    bool dst_active;            // 夏令时状态
} time_info_t;

/* 时间配置结构体 */
typedef struct {
    char timezone[32];          // 时区字符串
    bool auto_sync;             // 自动同步
    uint32_t sync_interval_s;   // 同步间隔(秒)
    bool dst_enabled;           // 夏令时开关
    char ntp_server[64];        // NTP服务器
} time_config_t;

/* 时间事件回调函数类型 */
typedef void (*time_event_callback_t)(time_info_t *time_info);

/**
 * @brief 初始化时间管理器
 * @return esp_err_t 
 */
esp_err_t time_manager_init(void);

/**
 * @brief 启动时间管理器
 * @return esp_err_t 
 */
esp_err_t time_manager_start(void);

/**
 * @brief 停止时间管理器
 * @return esp_err_t 
 */
esp_err_t time_manager_stop(void);

/**
 * @brief 获取当前时间信息
 * @return time_info_t* 
 */
time_info_t* time_manager_get_time_info(void);

/**
 * @brief 获取当前时间戳
 * @return time_t 
 */
time_t time_manager_get_timestamp(void);

/**
 * @brief 获取当前时间结构
 * @param tm 时间结构指针
 * @return esp_err_t 
 */
esp_err_t time_manager_get_localtime(struct tm *tm);

/**
 * @brief 手动设置时间
 * @param timestamp Unix时间戳
 * @return esp_err_t 
 */
esp_err_t time_manager_set_time(time_t timestamp);

/**
 * @brief 手动设置时间(结构体)
 * @param tm 时间结构
 * @return esp_err_t 
 */
esp_err_t time_manager_set_time_struct(struct tm *tm);

/**
 * @brief 同步网络时间
 * @return esp_err_t 
 */
esp_err_t time_manager_sync_ntp(void);

/**
 * @brief 同步RTC时间
 * @return esp_err_t 
 */
esp_err_t time_manager_sync_rtc(void);

/**
 * @brief 设置时区
 * @param timezone 时区字符串 (如 "Asia/Shanghai")
 * @return esp_err_t 
 */
esp_err_t time_manager_set_timezone(const char *timezone);

/**
 * @brief 获取时区
 * @return const char* 时区字符串
 */
const char* time_manager_get_timezone(void);

/**
 * @brief 设置时间配置
 * @param config 时间配置
 * @return esp_err_t 
 */
esp_err_t time_manager_set_config(const time_config_t *config);

/**
 * @brief 获取时间配置
 * @return time_config_t* 
 */
time_config_t* time_manager_get_config(void);

/**
 * @brief 注册时间事件回调
 * @param callback 回调函数
 * @return esp_err_t 
 */
esp_err_t time_manager_register_callback(time_event_callback_t callback);

/**
 * @brief 格式化时间字符串
 * @param format 格式字符串
 * @param buffer 输出缓冲区
 * @param size 缓冲区大小
 * @return esp_err_t 
 */
esp_err_t time_manager_format_time(const char *format, char *buffer, size_t size);

/**
 * @brief 获取星期几
 * @return int 星期几 (0=周日, 1=周一, ..., 6=周六)
 */
int time_manager_get_weekday(void);

/**
 * @brief 获取年份
 * @return int 年份
 */
int time_manager_get_year(void);

/**
 * @brief 获取月份
 * @return int 月份 (1-12)
 */
int time_manager_get_month(void);

/**
 * @brief 获取日期
 * @return int 日期 (1-31)
 */
int time_manager_get_day(void);

/**
 * @brief 获取小时
 * @return int 小时 (0-23)
 */
int time_manager_get_hour(void);

/**
 * @brief 获取分钟
 * @return int 分钟 (0-59)
 */
int time_manager_get_minute(void);

/**
 * @brief 获取秒
 * @return int 秒 (0-59)
 */
int time_manager_get_second(void);

/**
 * @brief 检查是否为闰年
 * @param year 年份
 * @return bool true=闰年, false=平年
 */
bool time_manager_is_leap_year(int year);

/**
 * @brief 获取月份天数
 * @param year 年份
 * @param month 月份
 * @return int 天数
 */
int time_manager_get_days_in_month(int year, int month);

#ifdef __cplusplus
}
#endif

#endif /* TIME_MANAGER_H */
