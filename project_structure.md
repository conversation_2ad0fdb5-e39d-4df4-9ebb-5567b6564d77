# TIMO智能闹钟项目结构重组

## 新的项目结构

```
TIMO_Project/
├── README.md                           # 项目总体说明
├── doc/                               # 项目文档
│   ├── README.md
│   ├── pin_conflict_analysis.md
│   └── software_user_manual.md
├── 1_main_device_firmware/            # 主体设备固件 (ESP32-S3)
│   ├── CMakeLists.txt
│   ├── sdkconfig
│   ├── main/
│   │   ├── CMakeLists.txt
│   │   ├── main.c
│   │   ├── app_main.c
│   │   └── include/
│   └── components/
│       ├── hardware/                  # 硬件抽象层
│       ├── system/                    # 系统管理
│       ├── ui/                        # 用户界面
│       ├── time_manager/              # 时间管理
│       ├── sensor_system/             # 传感器系统
│       ├── audio_system/              # 音频系统
│       └── bluetooth_system/          # 蓝牙通信
├── 2_base_station_firmware/           # 底座设备固件 (ESP32-C2)
│   ├── CMakeLists.txt
│   ├── sdkconfig
│   ├── main/
│   │   ├── CMakeLists.txt
│   │   ├── main.c
│   │   ├── base_main.c
│   │   ├── ws2812_driver.c
│   │   ├── sound_sensor.c
│   │   ├── button_handler.c
│   │   ├── bluetooth_comm.c
│   │   ├── ambient_effects.c
│   │   └── include/
│   └── components/
│       └── base_hardware/             # 底座硬件抽象层
├── 3_cloud_service/                   # 云端服务
│   ├── README.md
│   ├── package.json
│   ├── src/
│   │   ├── app.js
│   │   ├── routes/
│   │   ├── controllers/
│   │   ├── models/
│   │   └── middleware/
│   ├── config/
│   └── docs/
└── 4_wechat_miniprogram/             # 微信小程序
    ├── README.md
    ├── app.js
    ├── app.json
    ├── app.wxss
    ├── pages/
    │   ├── index/
    │   ├── device/
    │   └── settings/
    ├── components/
    ├── utils/
    └── images/
```

## 重组步骤

1. 创建新的目录结构
2. 移动现有的主体设备代码到 `1_main_device_firmware/`
3. 移动底座设备代码到 `2_base_station_firmware/`
4. 创建云端服务项目框架
5. 创建微信小程序项目框架
6. 更新文档和配置文件

## 各项目说明

### 1. 主体设备固件 (ESP32-S3)
- 基于ESP-IDF框架
- 包含完整的硬件驱动和系统管理
- 支持圆形LCD、传感器、音频、蓝牙等功能

### 2. 底座设备固件 (ESP32-C2)
- 基于ESP-IDF框架
- 专注于WS2812灯效控制和蓝牙通信
- 与主体设备协同工作

### 3. 云端服务
- 基于Node.js + Express框架
- 提供设备管理、数据同步、API服务
- 支持设备认证和状态监控

### 4. 微信小程序
- 基于微信小程序框架
- 提供设备控制和数据查看界面
- 支持蓝牙和网络连接
