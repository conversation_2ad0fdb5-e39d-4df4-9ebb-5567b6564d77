/**
 * @file ui_bluetooth.c
 * @brief TIMO蓝牙管理UI界面实现
 * @version 1.0.0
 * @date 2025-06-28
 */

#include "ui_bluetooth.h"
#include "network_manager.h"
#include "bt_scene_sync.h"
#include "esp_log.h"
#include <string.h>

static const char *TAG = "UI_BLUETOOTH";

/* UI对象 */
static lv_obj_t *g_bluetooth_page = NULL;
static lv_obj_t *g_status_label = NULL;
static lv_obj_t *g_device_list = NULL;
static lv_obj_t *g_scan_btn = NULL;
static lv_obj_t *g_connect_btn = NULL;
static lv_obj_t *g_disconnect_btn = NULL;
static lv_obj_t *g_progress_bar = NULL;
static lv_obj_t *g_progress_label = NULL;

/* 状态变量 */
static bool g_ui_initialized = false;
static bt_device_info_t g_selected_device = {0};
static bool g_device_selected = false;

/**
 * @brief 扫描按钮事件处理
 */
static void scan_btn_event_cb(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    
    if (code == LV_EVENT_CLICKED) {
        ESP_LOGI(TAG, "开始扫描底座设备");
        
        // 清空设备列表
        lv_list_clean(g_device_list);
        g_device_selected = false;
        lv_obj_add_state(g_connect_btn, LV_STATE_DISABLED);
        
        // 显示扫描进度
        ui_bluetooth_show_scanning(true);
        
        // 开始扫描
        esp_err_t ret = network_manager_rescan_base_devices();
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "扫描启动失败");
            ui_bluetooth_show_scanning(false);
            lv_label_set_text(g_status_label, "扫描启动失败");
        } else {
            lv_label_set_text(g_status_label, "正在扫描设备...");
        }
    }
}

/**
 * @brief 连接按钮事件处理
 */
static void connect_btn_event_cb(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    
    if (code == LV_EVENT_CLICKED && g_device_selected) {
        ESP_LOGI(TAG, "连接到设备: %s", g_selected_device.name);
        
        // 显示连接进度
        ui_bluetooth_show_connecting(true);
        lv_label_set_text(g_status_label, "正在连接...");
        
        // 开始连接
        esp_err_t ret = network_manager_connect_to_base(g_selected_device.addr);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "连接启动失败");
            ui_bluetooth_show_connecting(false);
            lv_label_set_text(g_status_label, "连接启动失败");
        }
    }
}

/**
 * @brief 断开连接按钮事件处理
 */
static void disconnect_btn_event_cb(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    
    if (code == LV_EVENT_CLICKED) {
        ESP_LOGI(TAG, "断开连接");
        
        // 断开连接
        esp_err_t ret = network_manager_disconnect_from_base();
        if (ret == ESP_OK) {
            lv_label_set_text(g_status_label, "已断开连接");
        } else {
            lv_label_set_text(g_status_label, "断开连接失败");
        }
    }
}

/**
 * @brief 设备列表项点击事件处理
 */
static void device_list_event_cb(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *obj = lv_event_get_target(e);
    
    if (code == LV_EVENT_CLICKED) {
        // 获取设备信息
        bt_device_info_t *device = (bt_device_info_t*)lv_obj_get_user_data(obj);
        if (device) {
            memcpy(&g_selected_device, device, sizeof(bt_device_info_t));
            g_device_selected = true;
            
            // 启用连接按钮
            lv_obj_clear_state(g_connect_btn, LV_STATE_DISABLED);
            
            ESP_LOGI(TAG, "选择设备: %s", device->name);
            
            char status_text[64];
            snprintf(status_text, sizeof(status_text), "已选择: %s", device->name);
            lv_label_set_text(g_status_label, status_text);
        }
    }
}

/**
 * @brief 初始化蓝牙管理UI
 */
esp_err_t ui_bluetooth_init(void)
{
    if (g_ui_initialized) {
        ESP_LOGW(TAG, "蓝牙管理UI已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化蓝牙管理UI...");
    
    g_ui_initialized = true;
    ESP_LOGI(TAG, "蓝牙管理UI初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 创建蓝牙管理页面
 */
lv_obj_t* ui_bluetooth_create_page(lv_obj_t *parent)
{
    if (g_bluetooth_page) {
        return g_bluetooth_page;
    }
    
    // 创建主容器
    g_bluetooth_page = lv_obj_create(parent);
    lv_obj_set_size(g_bluetooth_page, LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_pad_all(g_bluetooth_page, 10, 0);
    
    // 创建标题
    lv_obj_t *title = lv_label_create(g_bluetooth_page);
    lv_label_set_text(title, "蓝牙设备管理");
    lv_obj_set_style_text_font(title, &lv_font_montserrat_20, 0);
    lv_obj_align(title, LV_ALIGN_TOP_MID, 0, 0);
    
    // 创建状态标签
    g_status_label = lv_label_create(g_bluetooth_page);
    lv_label_set_text(g_status_label, "未连接");
    lv_obj_align_to(g_status_label, title, LV_ALIGN_OUT_BOTTOM_MID, 0, 10);
    
    // 创建按钮容器
    lv_obj_t *btn_cont = lv_obj_create(g_bluetooth_page);
    lv_obj_set_size(btn_cont, LV_PCT(100), 60);
    lv_obj_set_style_pad_all(btn_cont, 5, 0);
    lv_obj_align_to(btn_cont, g_status_label, LV_ALIGN_OUT_BOTTOM_MID, 0, 10);
    lv_obj_set_flex_flow(btn_cont, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(btn_cont, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    
    // 创建扫描按钮
    g_scan_btn = lv_btn_create(btn_cont);
    lv_obj_set_size(g_scan_btn, 80, 40);
    lv_obj_add_event_cb(g_scan_btn, scan_btn_event_cb, LV_EVENT_CLICKED, NULL);
    lv_obj_t *scan_label = lv_label_create(g_scan_btn);
    lv_label_set_text(scan_label, "扫描");
    lv_obj_center(scan_label);
    
    // 创建连接按钮
    g_connect_btn = lv_btn_create(btn_cont);
    lv_obj_set_size(g_connect_btn, 80, 40);
    lv_obj_add_event_cb(g_connect_btn, connect_btn_event_cb, LV_EVENT_CLICKED, NULL);
    lv_obj_add_state(g_connect_btn, LV_STATE_DISABLED);
    lv_obj_t *connect_label = lv_label_create(g_connect_btn);
    lv_label_set_text(connect_label, "连接");
    lv_obj_center(connect_label);
    
    // 创建断开连接按钮
    g_disconnect_btn = lv_btn_create(btn_cont);
    lv_obj_set_size(g_disconnect_btn, 80, 40);
    lv_obj_add_event_cb(g_disconnect_btn, disconnect_btn_event_cb, LV_EVENT_CLICKED, NULL);
    lv_obj_t *disconnect_label = lv_label_create(g_disconnect_btn);
    lv_label_set_text(disconnect_label, "断开");
    lv_obj_center(disconnect_label);
    
    // 创建设备列表
    g_device_list = lv_list_create(g_bluetooth_page);
    lv_obj_set_size(g_device_list, LV_PCT(100), LV_PCT(60));
    lv_obj_align_to(g_device_list, btn_cont, LV_ALIGN_OUT_BOTTOM_MID, 0, 10);
    
    // 创建进度条（初始隐藏）
    g_progress_bar = lv_bar_create(g_bluetooth_page);
    lv_obj_set_size(g_progress_bar, LV_PCT(80), 20);
    lv_obj_align(g_progress_bar, LV_ALIGN_CENTER, 0, 0);
    lv_obj_add_flag(g_progress_bar, LV_OBJ_FLAG_HIDDEN);
    
    // 创建进度标签（初始隐藏）
    g_progress_label = lv_label_create(g_bluetooth_page);
    lv_label_set_text(g_progress_label, "");
    lv_obj_align_to(g_progress_label, g_progress_bar, LV_ALIGN_OUT_BOTTOM_MID, 0, 5);
    lv_obj_add_flag(g_progress_label, LV_OBJ_FLAG_HIDDEN);
    
    // 初始隐藏页面
    lv_obj_add_flag(g_bluetooth_page, LV_OBJ_FLAG_HIDDEN);
    
    return g_bluetooth_page;
}

/**
 * @brief 显示蓝牙管理页面
 */
void ui_bluetooth_show_page(void)
{
    if (g_bluetooth_page) {
        lv_obj_clear_flag(g_bluetooth_page, LV_OBJ_FLAG_HIDDEN);
        
        // 更新当前状态
        bt_connection_state_t state = network_manager_get_bluetooth_state();
        ui_bluetooth_update_status(state);
    }
}

/**
 * @brief 隐藏蓝牙管理页面
 */
void ui_bluetooth_hide_page(void)
{
    if (g_bluetooth_page) {
        lv_obj_add_flag(g_bluetooth_page, LV_OBJ_FLAG_HIDDEN);
    }
}

/**
 * @brief 更新蓝牙连接状态显示
 */
void ui_bluetooth_update_status(bt_connection_state_t state)
{
    if (!g_status_label) {
        return;
    }
    
    const char *status_text;
    switch (state) {
        case BT_STATE_IDLE:
            status_text = "蓝牙空闲";
            break;
        case BT_STATE_SCANNING:
            status_text = "正在扫描...";
            break;
        case BT_STATE_CONNECTING:
            status_text = "正在连接...";
            break;
        case BT_STATE_CONNECTED:
            status_text = "已连接到底座";
            break;
        case BT_STATE_DISCONNECTED:
            status_text = "连接已断开";
            break;
        case BT_STATE_ERROR:
            status_text = "连接错误";
            break;
        default:
            status_text = "未知状态";
            break;
    }
    
    lv_label_set_text(g_status_label, status_text);
}

/**
 * @brief 更新扫描到的设备列表
 */
void ui_bluetooth_update_device_list(const bt_device_info_t *devices, uint8_t count)
{
    if (!g_device_list || !devices) {
        return;
    }

    ESP_LOGI(TAG, "更新设备列表，设备数量: %d", count);

    // 清空现有列表
    lv_list_clean(g_device_list);

    // 添加设备到列表
    for (uint8_t i = 0; i < count; i++) {
        const bt_device_info_t *device = &devices[i];

        // 创建列表项
        lv_obj_t *btn = lv_list_add_btn(g_device_list, LV_SYMBOL_BLUETOOTH, device->name);

        // 分配内存存储设备信息
        bt_device_info_t *device_data = malloc(sizeof(bt_device_info_t));
        if (device_data) {
            memcpy(device_data, device, sizeof(bt_device_info_t));
            lv_obj_set_user_data(btn, device_data);
        }

        // 添加点击事件
        lv_obj_add_event_cb(btn, device_list_event_cb, LV_EVENT_CLICKED, NULL);

        // 如果是目标设备，高亮显示
        if (device->is_target) {
            lv_obj_set_style_bg_color(btn, lv_color_hex(0x00AA00), LV_STATE_DEFAULT);
        }

        // 添加RSSI信息
        char rssi_text[32];
        snprintf(rssi_text, sizeof(rssi_text), "RSSI: %d dBm", device->rssi);
        lv_obj_t *rssi_label = lv_label_create(btn);
        lv_label_set_text(rssi_label, rssi_text);
        lv_obj_set_style_text_font(rssi_label, &lv_font_montserrat_12, 0);
        lv_obj_align(rssi_label, LV_ALIGN_RIGHT_MID, -10, 0);
    }

    // 隐藏扫描进度
    ui_bluetooth_show_scanning(false);

    if (count > 0) {
        lv_label_set_text(g_status_label, "扫描完成，请选择设备");
    } else {
        lv_label_set_text(g_status_label, "未发现设备");
    }
}

/**
 * @brief 显示连接进度
 */
void ui_bluetooth_show_connecting(bool connecting)
{
    if (!g_progress_bar || !g_progress_label) {
        return;
    }

    if (connecting) {
        lv_obj_clear_flag(g_progress_bar, LV_OBJ_FLAG_HIDDEN);
        lv_obj_clear_flag(g_progress_label, LV_OBJ_FLAG_HIDDEN);
        lv_label_set_text(g_progress_label, "正在连接...");

        // 设置进度条动画
        lv_bar_set_value(g_progress_bar, 0, LV_ANIM_OFF);
        lv_bar_set_value(g_progress_bar, 100, LV_ANIM_ON);

        // 禁用按钮
        lv_obj_add_state(g_scan_btn, LV_STATE_DISABLED);
        lv_obj_add_state(g_connect_btn, LV_STATE_DISABLED);
    } else {
        lv_obj_add_flag(g_progress_bar, LV_OBJ_FLAG_HIDDEN);
        lv_obj_add_flag(g_progress_label, LV_OBJ_FLAG_HIDDEN);

        // 启用按钮
        lv_obj_clear_state(g_scan_btn, LV_STATE_DISABLED);
        if (g_device_selected) {
            lv_obj_clear_state(g_connect_btn, LV_STATE_DISABLED);
        }
    }
}

/**
 * @brief 显示扫描进度
 */
void ui_bluetooth_show_scanning(bool scanning)
{
    if (!g_progress_bar || !g_progress_label) {
        return;
    }

    if (scanning) {
        lv_obj_clear_flag(g_progress_bar, LV_OBJ_FLAG_HIDDEN);
        lv_obj_clear_flag(g_progress_label, LV_OBJ_FLAG_HIDDEN);
        lv_label_set_text(g_progress_label, "正在扫描设备...");

        // 设置进度条动画
        lv_bar_set_value(g_progress_bar, 0, LV_ANIM_OFF);
        lv_bar_set_value(g_progress_bar, 100, LV_ANIM_ON);

        // 禁用按钮
        lv_obj_add_state(g_scan_btn, LV_STATE_DISABLED);
        lv_obj_add_state(g_connect_btn, LV_STATE_DISABLED);
    } else {
        lv_obj_add_flag(g_progress_bar, LV_OBJ_FLAG_HIDDEN);
        lv_obj_add_flag(g_progress_label, LV_OBJ_FLAG_HIDDEN);

        // 启用按钮
        lv_obj_clear_state(g_scan_btn, LV_STATE_DISABLED);
        if (g_device_selected) {
            lv_obj_clear_state(g_connect_btn, LV_STATE_DISABLED);
        }
    }
}

/**
 * @brief 测试氛围场景同步
 */
void ui_bluetooth_test_scene_sync(void)
{
    if (!network_manager_is_bluetooth_connected()) {
        ESP_LOGW(TAG, "蓝牙未连接，无法测试场景同步");
        return;
    }

    ESP_LOGI(TAG, "测试氛围场景同步");

    // 测试不同场景
    bt_scene_sync_standby(50);
    vTaskDelay(pdMS_TO_TICKS(2000));

    bt_scene_sync_conversation();
    vTaskDelay(pdMS_TO_TICKS(2000));

    bt_scene_sync_morning_wake();
    vTaskDelay(pdMS_TO_TICKS(3000));

    bt_scene_sync_standby(30);
}
