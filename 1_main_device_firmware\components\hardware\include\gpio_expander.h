/**
 * @file gpio_expander.h
 * @brief TCA9554PWR GPIO扩展芯片驱动头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef GPIO_EXPANDER_H
#define GPIO_EXPANDER_H

#include "esp_err.h"
#include "hardware_config.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化GPIO扩展芯片
 * @return esp_err_t 
 */
esp_err_t gpio_expander_init(void);

/**
 * @brief 设置GPIO扩展引脚电平
 * @param pin 扩展引脚编号
 * @param level 电平状态 (0或1)
 * @return esp_err_t 
 */
esp_err_t gpio_expander_set_level(exio_pin_t pin, uint8_t level);

/**
 * @brief 读取GPIO扩展引脚电平
 * @param pin 扩展引脚编号
 * @param level 读取到的电平状态
 * @return esp_err_t 
 */
esp_err_t gpio_expander_get_level(exio_pin_t pin, uint8_t *level);

/**
 * @brief 反初始化GPIO扩展芯片
 * @return esp_err_t 
 */
esp_err_t gpio_expander_deinit(void);

/* 便捷宏定义 */
#define LCD_RST_HIGH()      gpio_expander_set_level(EXIO1, 1)
#define LCD_RST_LOW()       gpio_expander_set_level(EXIO1, 0)
#define TOUCH_RST_HIGH()    gpio_expander_set_level(EXIO2, 1)
#define TOUCH_RST_LOW()     gpio_expander_set_level(EXIO2, 0)
#define LCD_CS_HIGH()       gpio_expander_set_level(EXIO3, 1)
#define LCD_CS_LOW()        gpio_expander_set_level(EXIO3, 0)
#define SD_CS_HIGH()        gpio_expander_set_level(EXIO4, 1)
#define SD_CS_LOW()         gpio_expander_set_level(EXIO4, 0)

#ifdef __cplusplus
}
#endif

#endif /* GPIO_EXPANDER_H */
