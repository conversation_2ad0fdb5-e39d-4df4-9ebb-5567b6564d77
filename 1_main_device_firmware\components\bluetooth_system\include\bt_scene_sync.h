/**
 * @file bt_scene_sync.h
 * @brief TIMO氛围场景同步模块头文件
 * @version 1.0.0
 * @date 2025-06-28
 */

#ifndef BT_SCENE_SYNC_H
#define BT_SCENE_SYNC_H

#include "esp_err.h"
#include "bt_protocol.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 场景同步状态 */
typedef enum {
    BT_SYNC_STATE_IDLE = 0,
    BT_SYNC_STATE_SYNCING,
    BT_SYNC_STATE_SYNCED,
    BT_SYNC_STATE_ERROR
} bt_sync_state_t;

/* 场景同步回调函数类型 */
typedef void (*bt_scene_sync_callback_t)(bt_scene_id_t scene_id, bool success);

/**
 * @brief 初始化场景同步模块
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_init(void);

/**
 * @brief 反初始化场景同步模块
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_deinit(void);

/**
 * @brief 同步氛围场景到底座
 * @param scene_id 场景ID
 * @param brightness 亮度 (0-100)
 * @param duration_ms 持续时间 (毫秒)
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_set_scene(bt_scene_id_t scene_id, uint8_t brightness, uint16_t duration_ms);

/**
 * @brief 设置自定义颜色场景
 * @param red 红色分量 (0-255)
 * @param green 绿色分量 (0-255)
 * @param blue 蓝色分量 (0-255)
 * @param brightness 亮度 (0-100)
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_set_custom_color(uint8_t red, uint8_t green, uint8_t blue, uint8_t brightness);

/**
 * @brief 开始音乐律动模式
 * @param mode 律动模式
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_start_music_mode(bt_music_mode_t mode);

/**
 * @brief 停止音乐律动模式
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_stop_music_mode(void);

/**
 * @brief 发送音频数据用于律动
 * @param volume 音量级别 (0-100)
 * @param spectrum_data 频谱数据 (8个频段)
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_send_audio_data(uint8_t volume, const uint8_t *spectrum_data);

/**
 * @brief 关闭底座LED
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_turn_off(void);

/**
 * @brief 开启底座LED
 * @param brightness 亮度 (0-100)
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_turn_on(uint8_t brightness);

/**
 * @brief 设置底座亮度
 * @param brightness 亮度 (0-100)
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_set_brightness(uint8_t brightness);

/**
 * @brief 获取底座状态
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_get_status(void);

/**
 * @brief 检查是否已同步
 * @return true 已同步
 * @return false 未同步
 */
bool bt_scene_sync_is_synced(void);

/**
 * @brief 获取同步状态
 * @return bt_sync_state_t 
 */
bt_sync_state_t bt_scene_sync_get_state(void);

/**
 * @brief 注册场景同步回调函数
 * @param callback 回调函数
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_register_callback(bt_scene_sync_callback_t callback);

/**
 * @brief 预定义场景快捷函数
 */

/**
 * @brief 设置待机模式
 * @param brightness 亮度 (0-100)
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_standby(uint8_t brightness);

/**
 * @brief 设置对话律动模式
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_conversation(void);

/**
 * @brief 设置晨间唤醒模式
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_morning_wake(void);

/**
 * @brief 设置小憩唤醒模式
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_nap_wake(void);

/**
 * @brief 设置助眠模式
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_sleep(void);

/**
 * @brief 设置待办提醒模式
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_todo_reminder(void);

/**
 * @brief 设置预警模式
 * @param level 预警级别 (0=低级, 1=中级, 2=高级)
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_warning(uint8_t level);

/**
 * @brief 设置专注模式
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_focus(void);

/**
 * @brief 设置充电状态模式
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_charging(void);

/**
 * @brief 设置配对模式
 * @return esp_err_t 
 */
esp_err_t bt_scene_sync_pairing(void);

#ifdef __cplusplus
}
#endif

#endif /* BT_SCENE_SYNC_H */
