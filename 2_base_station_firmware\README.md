# TIMO智能闹钟底座固件

## 项目概述

TIMO智能闹钟底座固件基于ESP32-C2开发，主要功能包括：

- **WS2812 RGB灯效控制** - 30颗LED氛围灯
- **声音传感器监测** - 环境声音检测和分析
- **用户按键处理** - 支持短按和长按检测
- **蓝牙通信** - 与主设备进行数据交互
- **氛围场景管理** - 多种预设灯效场景

## 硬件配置

### 主控芯片
- **ESP32-C2** (ESP8684H2)
- CPU频率: 120MHz
- 内存: 272KB SRAM
- Flash: 2MB

### 外设配置
- **WS2812 RGB灯带**: GPIO8, 30颗LED
- **声音传感器**: GPIO0 (ADC1_CH0)
- **用户按键**: GPIO9 (下拉输入)
- **状态LED**: GPIO18

## 软件架构

### 主要模块

1. **主程序** (`main.c`, `base_main.c`)
   - 系统初始化和主循环
   - 任务管理和事件处理

2. **WS2812驱动** (`ws2812_driver.c`)
   - LED颜色控制
   - 亮度调节
   - 特效算法

3. **声音传感器** (`sound_sensor.c`)
   - ADC采集
   - 数据处理和统计
   - 事件检测

4. **按键处理** (`button_handler.c`)
   - 防抖动处理
   - 长按检测
   - 事件队列管理

5. **蓝牙通信** (`bluetooth_comm.c`)
   - BLE服务器
   - GATT服务和特征值
   - 数据交互协议

6. **氛围灯效** (`ambient_effects.c`)
   - 场景管理
   - 灯效算法
   - 音乐同步

### 氛围场景

- `AMBIENT_SCENE_OFF` - 关闭
- `AMBIENT_SCENE_STANDBY` - 待机模式
- `AMBIENT_SCENE_CONVERSATION` - 对话律动
- `AMBIENT_SCENE_MORNING_WAKE` - 晨间唤醒
- `AMBIENT_SCENE_NAP_WAKE` - 小憩唤醒
- `AMBIENT_SCENE_SLEEP_AID` - 助眠模式
- `AMBIENT_SCENE_TODO_REMIND` - 待办提醒
- `AMBIENT_SCENE_ALERT_LOW/MEDIUM/HIGH` - 预警模式
- `AMBIENT_SCENE_FOCUS_MODE` - 专注模式
- `AMBIENT_SCENE_CHARGING` - 充电状态
- `AMBIENT_SCENE_PAIRING` - 配对模式

## 编译和烧录

### 环境要求

- ESP-IDF v5.0+
- Python 3.8+
- CMake 3.16+

### 编译步骤

1. **设置ESP-IDF环境**
   ```bash
   . $HOME/esp/esp-idf/export.sh
   ```

2. **配置项目**
   ```bash
   cd 2_base_station_firmware
   idf.py set-target esp32c2
   idf.py menuconfig  # 可选，使用默认配置
   ```

3. **编译项目**
   ```bash
   idf.py build
   ```

4. **烧录固件**
   ```bash
   idf.py -p /dev/ttyUSB0 flash monitor
   ```

### 配置说明

主要配置项在 `sdkconfig.defaults` 中：

- CPU频率: 120MHz
- 蓝牙: 启用BLE
- ADC: 启用校准
- 电源管理: 启用
- 日志级别: INFO

## 使用说明

### 蓝牙连接

设备名称: `TIMO_Base`
服务UUID: `12345678-1234-1234-1234-123456789abc`

特征值:
- 命令通道: `*************-4321-4321-cba987654321`
- 数据通道: `11111111-**************-************`

### 按键操作

- **短按**: 切换氛围场景
- **长按** (1秒): 进入配对模式

### LED指示

- **状态LED**: 系统运行状态指示
- **RGB灯带**: 氛围场景显示

## 调试信息

### 串口输出

波特率: 115200
日志标签:
- `BASE_MAIN`: 主程序
- `WS2812`: LED驱动
- `SOUND_SENSOR`: 声音传感器
- `BUTTON`: 按键处理
- `BT_COMM`: 蓝牙通信
- `AMBIENT`: 氛围灯效

### 性能监控

系统会定期输出:
- 内存使用情况
- 任务运行状态
- 蓝牙连接状态
- 声音级别统计

## 故障排除

### 常见问题

1. **编译错误**
   - 检查ESP-IDF版本
   - 确认依赖组件安装

2. **烧录失败**
   - 检查串口权限
   - 确认开发板连接

3. **蓝牙连接问题**
   - 检查蓝牙配置
   - 重启设备重新配对

4. **LED不亮**
   - 检查GPIO配置
   - 确认电源供应

### 调试命令

```bash
# 查看详细日志
idf.py monitor

# 清除配置重新编译
idf.py fullclean
idf.py build

# 擦除Flash
idf.py erase-flash
```

## 版本信息

- **版本**: v1.0.0
- **日期**: 2025-06-27
- **作者**: TIMO开发团队
