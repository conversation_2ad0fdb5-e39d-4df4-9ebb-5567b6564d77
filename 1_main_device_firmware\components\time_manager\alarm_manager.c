/**
 * @file alarm_manager.c
 * @brief TIMO闹钟管理器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "alarm_manager.h"
#include "time_manager.h"
#include "event_manager.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "nvs.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include <string.h>

static const char *TAG = "ALARM_MANAGER";
static const char *NVS_NAMESPACE = "alarms";

/* 闹钟管理器状态 */
static bool g_alarm_manager_initialized = false;
static bool g_alarm_manager_running = false;
static alarm_t g_alarms[MAX_ALARMS];
static uint8_t g_alarm_count = 0;
static alarm_event_callback_t g_alarm_callback = NULL;

/* 任务和互斥锁 */
static TaskHandle_t g_alarm_task_handle = NULL;
static SemaphoreHandle_t g_alarm_mutex = NULL;

/* 星期名称 */
static const char *WEEKDAY_NAMES[] = {"日", "一", "二", "三", "四", "五", "六"};
static const char *REPEAT_NAMES[] = {"不重复", "每天", "工作日", "周末", "每周", "自定义"};

/**
 * @brief 查找空闲的闹钟ID
 */
static uint8_t find_free_alarm_id(void)
{
    for (uint8_t id = 1; id <= MAX_ALARMS; id++) {
        bool found = false;
        for (uint8_t i = 0; i < g_alarm_count; i++) {
            if (g_alarms[i].id == id) {
                found = true;
                break;
            }
        }
        if (!found) {
            return id;
        }
    }
    return 0;  // 无可用ID
}

/**
 * @brief 查找闹钟索引
 */
static int find_alarm_index(uint8_t alarm_id)
{
    for (uint8_t i = 0; i < g_alarm_count; i++) {
        if (g_alarms[i].id == alarm_id) {
            return i;
        }
    }
    return -1;
}

/**
 * @brief 闹钟检查任务
 */
static void alarm_check_task(void *pvParameters)
{
    ESP_LOGI(TAG, "闹钟检查任务启动");
    
    while (g_alarm_manager_running) {
        time_t now = time_manager_get_timestamp();
        
        xSemaphoreTake(g_alarm_mutex, portMAX_DELAY);
        
        for (uint8_t i = 0; i < g_alarm_count; i++) {
            alarm_t *alarm = &g_alarms[i];
            
            if (alarm->status == ALARM_STATUS_ENABLED) {
                if (alarm_manager_should_trigger(alarm, now)) {
                    ESP_LOGI(TAG, "闹钟触发: ID=%d, 名称=%s", alarm->id, alarm->name);
                    
                    alarm->status = ALARM_STATUS_TRIGGERED;
                    alarm->last_triggered = now;
                    alarm->snooze_count = 0;
                    
                    // 计算下次触发时间
                    alarm->next_trigger = alarm_manager_calculate_next_trigger(alarm);
                    
                    // 发送事件
                    if (g_alarm_callback) {
                        g_alarm_callback(ALARM_EVENT_TRIGGERED, alarm);
                    }
                    event_manager_post_event(EVENT_TYPE_ALARM, ALARM_EVENT_TRIGGERED, alarm);
                }
            } else if (alarm->status == ALARM_STATUS_SNOOZED) {
                // 检查贪睡时间是否到了
                time_t snooze_end = alarm->last_triggered + (alarm->snooze_duration * 60);
                if (now >= snooze_end) {
                    ESP_LOGI(TAG, "贪睡结束，重新触发闹钟: ID=%d", alarm->id);
                    
                    alarm->status = ALARM_STATUS_TRIGGERED;
                    alarm->last_triggered = now;
                    
                    if (g_alarm_callback) {
                        g_alarm_callback(ALARM_EVENT_TRIGGERED, alarm);
                    }
                    event_manager_post_event(EVENT_TYPE_ALARM, ALARM_EVENT_TRIGGERED, alarm);
                }
            }
        }
        
        xSemaphoreGive(g_alarm_mutex);
        
        // 每秒检查一次
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
    
    ESP_LOGI(TAG, "闹钟检查任务结束");
    vTaskDelete(NULL);
}

/**
 * @brief 初始化闹钟管理器
 */
esp_err_t alarm_manager_init(void)
{
    if (g_alarm_manager_initialized) {
        ESP_LOGW(TAG, "闹钟管理器已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化闹钟管理器...");
    
    // 创建互斥锁
    g_alarm_mutex = xSemaphoreCreateMutex();
    if (!g_alarm_mutex) {
        ESP_LOGE(TAG, "创建闹钟互斥锁失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 初始化闹钟数组
    memset(g_alarms, 0, sizeof(g_alarms));
    g_alarm_count = 0;
    
    // 加载闹钟配置
    alarm_manager_load_config();
    
    g_alarm_manager_initialized = true;
    ESP_LOGI(TAG, "闹钟管理器初始化完成，加载了 %d 个闹钟", g_alarm_count);
    
    return ESP_OK;
}

/**
 * @brief 启动闹钟管理器
 */
esp_err_t alarm_manager_start(void)
{
    if (!g_alarm_manager_initialized) {
        ESP_LOGE(TAG, "闹钟管理器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (g_alarm_manager_running) {
        ESP_LOGW(TAG, "闹钟管理器已启动");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "启动闹钟管理器...");
    
    // 创建闹钟检查任务
    BaseType_t ret = xTaskCreate(
        alarm_check_task,
        "alarm_check",
        4096,
        NULL,
        6,  // 高优先级
        &g_alarm_task_handle
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建闹钟检查任务失败");
        return ESP_ERR_NO_MEM;
    }
    
    g_alarm_manager_running = true;
    ESP_LOGI(TAG, "闹钟管理器启动完成");
    
    return ESP_OK;
}

/**
 * @brief 停止闹钟管理器
 */
esp_err_t alarm_manager_stop(void)
{
    if (!g_alarm_manager_running) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "停止闹钟管理器...");
    
    g_alarm_manager_running = false;
    
    // 停止闹钟检查任务
    if (g_alarm_task_handle) {
        vTaskDelete(g_alarm_task_handle);
        g_alarm_task_handle = NULL;
    }
    
    ESP_LOGI(TAG, "闹钟管理器停止完成");
    return ESP_OK;
}

/**
 * @brief 创建闹钟
 */
esp_err_t alarm_manager_create(alarm_t *alarm)
{
    if (!alarm) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (g_alarm_count >= MAX_ALARMS) {
        ESP_LOGE(TAG, "闹钟数量已达上限");
        return ESP_ERR_NO_MEM;
    }
    
    xSemaphoreTake(g_alarm_mutex, portMAX_DELAY);
    
    // 分配ID
    uint8_t id = find_free_alarm_id();
    if (id == 0) {
        xSemaphoreGive(g_alarm_mutex);
        ESP_LOGE(TAG, "无可用的闹钟ID");
        return ESP_ERR_NO_MEM;
    }
    
    alarm->id = id;
    alarm->created_time = time_manager_get_timestamp();
    alarm->next_trigger = alarm_manager_calculate_next_trigger(alarm);
    
    // 添加到数组
    memcpy(&g_alarms[g_alarm_count], alarm, sizeof(alarm_t));
    g_alarm_count++;
    
    xSemaphoreGive(g_alarm_mutex);
    
    // 保存配置
    alarm_manager_save_config();
    
    ESP_LOGI(TAG, "创建闹钟成功: ID=%d, 名称=%s, 时间=%02d:%02d", 
             alarm->id, alarm->name, alarm->hour, alarm->minute);
    
    return ESP_OK;
}

/**
 * @brief 删除闹钟
 */
esp_err_t alarm_manager_delete(uint8_t alarm_id)
{
    xSemaphoreTake(g_alarm_mutex, portMAX_DELAY);
    
    int index = find_alarm_index(alarm_id);
    if (index < 0) {
        xSemaphoreGive(g_alarm_mutex);
        ESP_LOGE(TAG, "未找到闹钟: ID=%d", alarm_id);
        return ESP_ERR_NOT_FOUND;
    }
    
    // 移除闹钟
    for (int i = index; i < g_alarm_count - 1; i++) {
        memcpy(&g_alarms[i], &g_alarms[i + 1], sizeof(alarm_t));
    }
    g_alarm_count--;
    
    xSemaphoreGive(g_alarm_mutex);
    
    // 保存配置
    alarm_manager_save_config();
    
    ESP_LOGI(TAG, "删除闹钟成功: ID=%d", alarm_id);
    return ESP_OK;
}

/**
 * @brief 更新闹钟
 */
esp_err_t alarm_manager_update(const alarm_t *alarm)
{
    if (!alarm) {
        return ESP_ERR_INVALID_ARG;
    }
    
    xSemaphoreTake(g_alarm_mutex, portMAX_DELAY);
    
    int index = find_alarm_index(alarm->id);
    if (index < 0) {
        xSemaphoreGive(g_alarm_mutex);
        ESP_LOGE(TAG, "未找到闹钟: ID=%d", alarm->id);
        return ESP_ERR_NOT_FOUND;
    }
    
    // 更新闹钟
    memcpy(&g_alarms[index], alarm, sizeof(alarm_t));
    g_alarms[index].next_trigger = alarm_manager_calculate_next_trigger(&g_alarms[index]);
    
    xSemaphoreGive(g_alarm_mutex);
    
    // 保存配置
    alarm_manager_save_config();
    
    ESP_LOGI(TAG, "更新闹钟成功: ID=%d", alarm->id);
    return ESP_OK;
}

/**
 * @brief 启用闹钟
 */
esp_err_t alarm_manager_enable(uint8_t alarm_id)
{
    xSemaphoreTake(g_alarm_mutex, portMAX_DELAY);
    
    int index = find_alarm_index(alarm_id);
    if (index < 0) {
        xSemaphoreGive(g_alarm_mutex);
        return ESP_ERR_NOT_FOUND;
    }
    
    g_alarms[index].status = ALARM_STATUS_ENABLED;
    g_alarms[index].next_trigger = alarm_manager_calculate_next_trigger(&g_alarms[index]);
    
    xSemaphoreGive(g_alarm_mutex);
    
    alarm_manager_save_config();
    ESP_LOGI(TAG, "启用闹钟: ID=%d", alarm_id);
    
    return ESP_OK;
}

/**
 * @brief 禁用闹钟
 */
esp_err_t alarm_manager_disable(uint8_t alarm_id)
{
    xSemaphoreTake(g_alarm_mutex, portMAX_DELAY);
    
    int index = find_alarm_index(alarm_id);
    if (index < 0) {
        xSemaphoreGive(g_alarm_mutex);
        return ESP_ERR_NOT_FOUND;
    }
    
    g_alarms[index].status = ALARM_STATUS_DISABLED;
    
    xSemaphoreGive(g_alarm_mutex);
    
    alarm_manager_save_config();
    ESP_LOGI(TAG, "禁用闹钟: ID=%d", alarm_id);
    
    return ESP_OK;
}

/**
 * @brief 获取闹钟
 */
alarm_t* alarm_manager_get(uint8_t alarm_id)
{
    int index = find_alarm_index(alarm_id);
    if (index < 0) {
        return NULL;
    }
    
    return &g_alarms[index];
}

/**
 * @brief 获取所有闹钟
 */
esp_err_t alarm_manager_get_all(alarm_t *alarms, uint8_t *count)
{
    if (!alarms || !count) {
        return ESP_ERR_INVALID_ARG;
    }
    
    xSemaphoreTake(g_alarm_mutex, portMAX_DELAY);
    
    uint8_t copy_count = (*count < g_alarm_count) ? *count : g_alarm_count;
    memcpy(alarms, g_alarms, copy_count * sizeof(alarm_t));
    *count = copy_count;
    
    xSemaphoreGive(g_alarm_mutex);
    
    return ESP_OK;
}

/**
 * @brief 获取启用的闹钟数量
 */
uint8_t alarm_manager_get_enabled_count(void)
{
    uint8_t count = 0;
    
    xSemaphoreTake(g_alarm_mutex, portMAX_DELAY);
    
    for (uint8_t i = 0; i < g_alarm_count; i++) {
        if (g_alarms[i].status == ALARM_STATUS_ENABLED) {
            count++;
        }
    }
    
    xSemaphoreGive(g_alarm_mutex);
    
    return count;
}

/**
 * @brief 获取下一个闹钟
 */
alarm_t* alarm_manager_get_next(void)
{
    alarm_t *next_alarm = NULL;
    time_t earliest_time = 0;
    
    xSemaphoreTake(g_alarm_mutex, portMAX_DELAY);
    
    for (uint8_t i = 0; i < g_alarm_count; i++) {
        if (g_alarms[i].status == ALARM_STATUS_ENABLED) {
            if (earliest_time == 0 || g_alarms[i].next_trigger < earliest_time) {
                earliest_time = g_alarms[i].next_trigger;
                next_alarm = &g_alarms[i];
            }
        }
    }
    
    xSemaphoreGive(g_alarm_mutex);
    
    return next_alarm;
}

/**
 * @brief 贪睡闹钟
 */
esp_err_t alarm_manager_snooze(uint8_t alarm_id)
{
    xSemaphoreTake(g_alarm_mutex, portMAX_DELAY);
    
    int index = find_alarm_index(alarm_id);
    if (index < 0) {
        xSemaphoreGive(g_alarm_mutex);
        return ESP_ERR_NOT_FOUND;
    }
    
    alarm_t *alarm = &g_alarms[index];
    
    if (alarm->status != ALARM_STATUS_TRIGGERED) {
        xSemaphoreGive(g_alarm_mutex);
        ESP_LOGW(TAG, "闹钟未处于触发状态，无法贪睡: ID=%d", alarm_id);
        return ESP_ERR_INVALID_STATE;
    }
    
    if (alarm->snooze_count >= alarm->max_snooze) {
        xSemaphoreGive(g_alarm_mutex);
        ESP_LOGW(TAG, "已达最大贪睡次数: ID=%d", alarm_id);
        return ESP_ERR_INVALID_STATE;
    }
    
    alarm->status = ALARM_STATUS_SNOOZED;
    alarm->snooze_count++;
    
    xSemaphoreGive(g_alarm_mutex);
    
    ESP_LOGI(TAG, "闹钟贪睡: ID=%d, 次数=%d/%d", alarm_id, alarm->snooze_count, alarm->max_snooze);
    
    if (g_alarm_callback) {
        g_alarm_callback(ALARM_EVENT_SNOOZED, alarm);
    }
    event_manager_post_event(EVENT_TYPE_ALARM, ALARM_EVENT_SNOOZED, alarm);
    
    return ESP_OK;
}

/**
 * @brief 关闭闹钟
 */
esp_err_t alarm_manager_dismiss(uint8_t alarm_id)
{
    xSemaphoreTake(g_alarm_mutex, portMAX_DELAY);
    
    int index = find_alarm_index(alarm_id);
    if (index < 0) {
        xSemaphoreGive(g_alarm_mutex);
        return ESP_ERR_NOT_FOUND;
    }
    
    alarm_t *alarm = &g_alarms[index];
    
    if (alarm->repeat == ALARM_REPEAT_NONE) {
        alarm->status = ALARM_STATUS_DISABLED;
    } else {
        alarm->status = ALARM_STATUS_ENABLED;
        alarm->next_trigger = alarm_manager_calculate_next_trigger(alarm);
    }
    
    alarm->snooze_count = 0;
    
    xSemaphoreGive(g_alarm_mutex);
    
    ESP_LOGI(TAG, "关闭闹钟: ID=%d", alarm_id);
    
    if (g_alarm_callback) {
        g_alarm_callback(ALARM_EVENT_DISMISSED, alarm);
    }
    event_manager_post_event(EVENT_TYPE_ALARM, ALARM_EVENT_DISMISSED, alarm);
    
    return ESP_OK;
}

/**
 * @brief 注册闹钟事件回调
 */
esp_err_t alarm_manager_register_callback(alarm_event_callback_t callback)
{
    g_alarm_callback = callback;
    return ESP_OK;
}

/**
 * @brief 保存闹钟配置
 */
esp_err_t alarm_manager_save_config(void)
{
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "打开NVS失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 保存闹钟数量
    ret = nvs_set_u8(nvs_handle, "count", g_alarm_count);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "保存闹钟数量失败");
        nvs_close(nvs_handle);
        return ret;
    }
    
    // 保存闹钟数据
    ret = nvs_set_blob(nvs_handle, "alarms", g_alarms, g_alarm_count * sizeof(alarm_t));
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "保存闹钟数据失败");
        nvs_close(nvs_handle);
        return ret;
    }
    
    ret = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "闹钟配置保存成功，共 %d 个闹钟", g_alarm_count);
    }
    
    return ret;
}

/**
 * @brief 加载闹钟配置
 */
esp_err_t alarm_manager_load_config(void)
{
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open(NVS_NAMESPACE, NVS_READONLY, &nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "打开NVS失败，使用默认配置: %s", esp_err_to_name(ret));
        return ESP_OK;  // 首次运行，没有配置文件是正常的
    }
    
    // 读取闹钟数量
    ret = nvs_get_u8(nvs_handle, "count", &g_alarm_count);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "读取闹钟数量失败");
        nvs_close(nvs_handle);
        return ESP_OK;
    }
    
    if (g_alarm_count > MAX_ALARMS) {
        ESP_LOGW(TAG, "闹钟数量超出限制，重置为0");
        g_alarm_count = 0;
        nvs_close(nvs_handle);
        return ESP_OK;
    }
    
    // 读取闹钟数据
    size_t required_size = g_alarm_count * sizeof(alarm_t);
    ret = nvs_get_blob(nvs_handle, "alarms", g_alarms, &required_size);
    
    nvs_close(nvs_handle);
    
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "读取闹钟数据失败，重置配置");
        g_alarm_count = 0;
        return ESP_OK;
    }
    
    // 重新计算下次触发时间
    for (uint8_t i = 0; i < g_alarm_count; i++) {
        g_alarms[i].next_trigger = alarm_manager_calculate_next_trigger(&g_alarms[i]);
    }
    
    ESP_LOGI(TAG, "闹钟配置加载成功，共 %d 个闹钟", g_alarm_count);
    return ESP_OK;
}

/**
 * @brief 计算下次触发时间
 */
time_t alarm_manager_calculate_next_trigger(const alarm_t *alarm)
{
    if (!alarm || alarm->status != ALARM_STATUS_ENABLED) {
        return 0;
    }
    
    time_t now = time_manager_get_timestamp();
    struct tm now_tm;
    localtime_r(&now, &now_tm);
    
    struct tm trigger_tm = now_tm;
    trigger_tm.tm_hour = alarm->hour;
    trigger_tm.tm_min = alarm->minute;
    trigger_tm.tm_sec = 0;
    
    time_t trigger_time = mktime(&trigger_tm);
    
    // 如果今天的时间已过，计算下次触发时间
    if (trigger_time <= now) {
        switch (alarm->repeat) {
            case ALARM_REPEAT_NONE:
                return 0;  // 不重复的闹钟，时间已过则不再触发
                
            case ALARM_REPEAT_DAILY:
                trigger_time += 24 * 3600;  // 明天同一时间
                break;
                
            case ALARM_REPEAT_WEEKDAYS:
                do {
                    trigger_time += 24 * 3600;
                    localtime_r(&trigger_time, &trigger_tm);
                } while (trigger_tm.tm_wday == 0 || trigger_tm.tm_wday == 6);  // 跳过周末
                break;
                
            case ALARM_REPEAT_WEEKENDS:
                do {
                    trigger_time += 24 * 3600;
                    localtime_r(&trigger_time, &trigger_tm);
                } while (trigger_tm.tm_wday != 0 && trigger_tm.tm_wday != 6);  // 只在周末
                break;
                
            case ALARM_REPEAT_WEEKLY:
                trigger_time += 7 * 24 * 3600;  // 下周同一时间
                break;
                
            case ALARM_REPEAT_CUSTOM:
                do {
                    trigger_time += 24 * 3600;
                    localtime_r(&trigger_time, &trigger_tm);
                } while (!(alarm->weekdays & (1 << trigger_tm.tm_wday)));
                break;
                
            default:
                return 0;
        }
    }
    
    return trigger_time;
}

/**
 * @brief 检查闹钟是否应该在指定时间触发
 */
bool alarm_manager_should_trigger(const alarm_t *alarm, time_t timestamp)
{
    if (!alarm || alarm->status != ALARM_STATUS_ENABLED) {
        return false;
    }
    
    struct tm tm;
    localtime_r(&timestamp, &tm);
    
    // 检查时间是否匹配
    if (tm.tm_hour != alarm->hour || tm.tm_min != alarm->minute || tm.tm_sec != 0) {
        return false;
    }
    
    // 检查重复模式
    switch (alarm->repeat) {
        case ALARM_REPEAT_NONE:
            return (timestamp >= alarm->next_trigger && timestamp < alarm->next_trigger + 60);
            
        case ALARM_REPEAT_DAILY:
            return true;
            
        case ALARM_REPEAT_WEEKDAYS:
            return (tm.tm_wday >= 1 && tm.tm_wday <= 5);
            
        case ALARM_REPEAT_WEEKENDS:
            return (tm.tm_wday == 0 || tm.tm_wday == 6);
            
        case ALARM_REPEAT_WEEKLY:
            return true;
            
        case ALARM_REPEAT_CUSTOM:
            return (alarm->weekdays & (1 << tm.tm_wday)) != 0;
            
        default:
            return false;
    }
}

/**
 * @brief 格式化闹钟时间字符串
 */
esp_err_t alarm_manager_format_time(const alarm_t *alarm, char *buffer, size_t size)
{
    if (!alarm || !buffer || size == 0) {
        return ESP_ERR_INVALID_ARG;
    }
    
    snprintf(buffer, size, "%02d:%02d", alarm->hour, alarm->minute);
    return ESP_OK;
}

/**
 * @brief 格式化重复模式字符串
 */
esp_err_t alarm_manager_format_repeat(alarm_repeat_t repeat, uint8_t weekdays, char *buffer, size_t size)
{
    if (!buffer || size == 0) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (repeat < ALARM_REPEAT_MAX) {
        if (repeat == ALARM_REPEAT_CUSTOM) {
            // 自定义模式，显示具体星期
            char *ptr = buffer;
            size_t remaining = size;
            bool first = true;
            
            for (int i = 0; i < 7; i++) {
                if (weekdays & (1 << i)) {
                    if (!first && remaining > 1) {
                        *ptr++ = ',';
                        remaining--;
                    }
                    int len = snprintf(ptr, remaining, "%s", WEEKDAY_NAMES[i]);
                    if (len > 0 && len < remaining) {
                        ptr += len;
                        remaining -= len;
                    }
                    first = false;
                }
            }
        } else {
            snprintf(buffer, size, "%s", REPEAT_NAMES[repeat]);
        }
        return ESP_OK;
    }
    
    return ESP_ERR_INVALID_ARG;
}
