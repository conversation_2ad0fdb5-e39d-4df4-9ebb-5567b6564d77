/**
 * @file audio_system.c
 * @brief TIMO音频系统实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "audio_system.h"
#include "audio_recorder.h"
#include "audio_player.h"
#include "audio_effects.h"
#include "music_manager.h"
#include "voice_processor.h"
#include "hardware_hal.h"
#include "system_manager.h"
#include "event_manager.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

static const char *TAG = "AUDIO_SYSTEM";

/* 音频系统状态 */
static bool g_audio_system_initialized = false;
static bool g_audio_system_running = false;
static audio_state_t g_audio_state = AUDIO_STATE_IDLE;
static audio_config_t g_audio_config;
static audio_event_callback_t g_audio_callback = NULL;

/* 互斥锁 */
static SemaphoreHandle_t g_audio_mutex = NULL;

/* 默认音频配置 */
static const audio_config_t DEFAULT_AUDIO_CONFIG = {
    .sample_rate = 16000,
    .bits_per_sample = 16,
    .channels = 1,
    .volume = 50,
    .auto_gain_control = true,
    .noise_suppression = true,
    .echo_cancellation = true
};

/**
 * @brief 发送音频事件
 */
static void send_audio_event(audio_event_t event, void *data)
{
    if (g_audio_callback) {
        g_audio_callback(event, data);
    }
    
    event_manager_post_event(EVENT_TYPE_AUDIO, event, data);
}

/**
 * @brief 初始化音频系统
 */
esp_err_t audio_system_init(void)
{
    if (g_audio_system_initialized) {
        ESP_LOGW(TAG, "音频系统已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化音频系统...");
    
    // 创建互斥锁
    g_audio_mutex = xSemaphoreCreateMutex();
    if (!g_audio_mutex) {
        ESP_LOGE(TAG, "创建音频互斥锁失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 初始化音频配置
    memcpy(&g_audio_config, &DEFAULT_AUDIO_CONFIG, sizeof(audio_config_t));
    
    // 初始化I2S音频接口
    esp_err_t ret = hardware_i2s_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2S音频接口初始化失败");
        return ret;
    }
    
    // 初始化音频编解码器
    ret = audio_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "音频编解码器初始化失败");
        return ret;
    }
    
    // 初始化子模块
    ret = audio_recorder_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "音频录制器初始化失败");
        return ret;
    }
    
    ret = audio_player_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "音频播放器初始化失败");
        return ret;
    }
    
    ret = audio_effects_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "音频效果器初始化失败");
        return ret;
    }
    
    ret = music_manager_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "音乐管理器初始化失败");
        return ret;
    }
    
    ret = voice_processor_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "语音处理器初始化失败");
        return ret;
    }
    
    g_audio_system_initialized = true;
    ESP_LOGI(TAG, "音频系统初始化完成");
    
    return ESP_OK;
}

/**
 * @brief 启动音频系统
 */
esp_err_t audio_system_start(void)
{
    if (!g_audio_system_initialized) {
        ESP_LOGE(TAG, "音频系统未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (g_audio_system_running) {
        ESP_LOGW(TAG, "音频系统已启动");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "启动音频系统...");
    
    // 启动子模块
    audio_recorder_start();
    audio_player_start();
    audio_effects_start();
    music_manager_start();
    voice_processor_start();
    
    g_audio_system_running = true;
    g_audio_state = AUDIO_STATE_IDLE;
    
    ESP_LOGI(TAG, "音频系统启动完成");
    return ESP_OK;
}

/**
 * @brief 停止音频系统
 */
esp_err_t audio_system_stop(void)
{
    if (!g_audio_system_running) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "停止音频系统...");
    
    // 停止当前播放或录音
    audio_system_stop_play();
    audio_system_stop_record();
    
    // 停止子模块
    voice_processor_stop();
    music_manager_stop();
    audio_effects_stop();
    audio_player_stop();
    audio_recorder_stop();
    
    g_audio_system_running = false;
    g_audio_state = AUDIO_STATE_IDLE;
    
    ESP_LOGI(TAG, "音频系统停止完成");
    return ESP_OK;
}

/**
 * @brief 播放音频文件
 */
esp_err_t audio_system_play_file(const char *file_path)
{
    if (!file_path) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_audio_system_running) {
        ESP_LOGE(TAG, "音频系统未运行");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "播放音频文件: %s", file_path);
    
    xSemaphoreTake(g_audio_mutex, portMAX_DELAY);
    
    // 停止当前播放
    if (g_audio_state == AUDIO_STATE_PLAYING) {
        audio_player_stop();
    }
    
    // 开始播放新文件
    esp_err_t ret = audio_player_play_file(file_path);
    if (ret == ESP_OK) {
        g_audio_state = AUDIO_STATE_PLAYING;
        send_audio_event(AUDIO_EVENT_PLAY_START, (void*)file_path);
    } else {
        g_audio_state = AUDIO_STATE_ERROR;
        send_audio_event(AUDIO_EVENT_ERROR, NULL);
    }
    
    xSemaphoreGive(g_audio_mutex);
    
    return ret;
}

/**
 * @brief 播放音频数据
 */
esp_err_t audio_system_play_data(const uint8_t *data, size_t size, audio_format_t format)
{
    if (!data || size == 0) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_audio_system_running) {
        ESP_LOGE(TAG, "音频系统未运行");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "播放音频数据: %d bytes, 格式=%d", size, format);
    
    xSemaphoreTake(g_audio_mutex, portMAX_DELAY);
    
    esp_err_t ret = audio_player_play_data(data, size, format);
    if (ret == ESP_OK) {
        g_audio_state = AUDIO_STATE_PLAYING;
        send_audio_event(AUDIO_EVENT_PLAY_START, NULL);
    } else {
        g_audio_state = AUDIO_STATE_ERROR;
        send_audio_event(AUDIO_EVENT_ERROR, NULL);
    }
    
    xSemaphoreGive(g_audio_mutex);
    
    return ret;
}

/**
 * @brief 停止播放
 */
esp_err_t audio_system_stop_play(void)
{
    if (g_audio_state != AUDIO_STATE_PLAYING && g_audio_state != AUDIO_STATE_PAUSED) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "停止播放");
    
    xSemaphoreTake(g_audio_mutex, portMAX_DELAY);
    
    esp_err_t ret = audio_player_stop();
    g_audio_state = AUDIO_STATE_IDLE;
    send_audio_event(AUDIO_EVENT_PLAY_STOP, NULL);
    
    xSemaphoreGive(g_audio_mutex);
    
    return ret;
}

/**
 * @brief 暂停播放
 */
esp_err_t audio_system_pause_play(void)
{
    if (g_audio_state != AUDIO_STATE_PLAYING) {
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "暂停播放");
    
    xSemaphoreTake(g_audio_mutex, portMAX_DELAY);
    
    esp_err_t ret = audio_player_pause();
    if (ret == ESP_OK) {
        g_audio_state = AUDIO_STATE_PAUSED;
        send_audio_event(AUDIO_EVENT_PLAY_PAUSE, NULL);
    }
    
    xSemaphoreGive(g_audio_mutex);
    
    return ret;
}

/**
 * @brief 恢复播放
 */
esp_err_t audio_system_resume_play(void)
{
    if (g_audio_state != AUDIO_STATE_PAUSED) {
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "恢复播放");
    
    xSemaphoreTake(g_audio_mutex, portMAX_DELAY);
    
    esp_err_t ret = audio_player_resume();
    if (ret == ESP_OK) {
        g_audio_state = AUDIO_STATE_PLAYING;
        send_audio_event(AUDIO_EVENT_PLAY_RESUME, NULL);
    }
    
    xSemaphoreGive(g_audio_mutex);
    
    return ret;
}

/**
 * @brief 开始录音
 */
esp_err_t audio_system_start_record(const char *file_path, uint32_t duration_ms)
{
    if (!file_path) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_audio_system_running) {
        ESP_LOGE(TAG, "音频系统未运行");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "开始录音: %s, 时长=%d ms", file_path, duration_ms);
    
    xSemaphoreTake(g_audio_mutex, portMAX_DELAY);
    
    esp_err_t ret = audio_recorder_start(file_path, duration_ms);
    if (ret == ESP_OK) {
        g_audio_state = AUDIO_STATE_RECORDING;
        send_audio_event(AUDIO_EVENT_RECORD_START, (void*)file_path);
    } else {
        g_audio_state = AUDIO_STATE_ERROR;
        send_audio_event(AUDIO_EVENT_ERROR, NULL);
    }
    
    xSemaphoreGive(g_audio_mutex);
    
    return ret;
}

/**
 * @brief 停止录音
 */
esp_err_t audio_system_stop_record(void)
{
    if (g_audio_state != AUDIO_STATE_RECORDING) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "停止录音");
    
    xSemaphoreTake(g_audio_mutex, portMAX_DELAY);
    
    esp_err_t ret = audio_recorder_stop();
    g_audio_state = AUDIO_STATE_IDLE;
    send_audio_event(AUDIO_EVENT_RECORD_STOP, NULL);
    
    xSemaphoreGive(g_audio_mutex);
    
    return ret;
}

/**
 * @brief 设置音量
 */
esp_err_t audio_system_set_volume(uint8_t volume)
{
    if (volume > 100) {
        volume = 100;
    }
    
    ESP_LOGI(TAG, "设置音量: %d%%", volume);
    
    xSemaphoreTake(g_audio_mutex, portMAX_DELAY);
    
    g_audio_config.volume = volume;
    esp_err_t ret = audio_set_volume(volume);
    
    xSemaphoreGive(g_audio_mutex);
    
    if (ret == ESP_OK) {
        send_audio_event(AUDIO_EVENT_VOLUME_CHANGE, &volume);
    }
    
    return ret;
}

/**
 * @brief 获取音量
 */
uint8_t audio_system_get_volume(void)
{
    return g_audio_config.volume;
}

/**
 * @brief 设置音频配置
 */
esp_err_t audio_system_set_config(const audio_config_t *config)
{
    if (!config) {
        return ESP_ERR_INVALID_ARG;
    }
    
    xSemaphoreTake(g_audio_mutex, portMAX_DELAY);
    memcpy(&g_audio_config, config, sizeof(audio_config_t));
    xSemaphoreGive(g_audio_mutex);
    
    ESP_LOGI(TAG, "音频配置已更新");
    return ESP_OK;
}

/**
 * @brief 获取音频配置
 */
audio_config_t* audio_system_get_config(void)
{
    return &g_audio_config;
}

/**
 * @brief 获取音频状态
 */
audio_state_t audio_system_get_state(void)
{
    return g_audio_state;
}

/**
 * @brief 注册音频事件回调
 */
esp_err_t audio_system_register_callback(audio_event_callback_t callback)
{
    g_audio_callback = callback;
    return ESP_OK;
}

/**
 * @brief 播放提示音
 */
esp_err_t audio_system_play_tone(uint8_t tone_id)
{
    ESP_LOGI(TAG, "播放提示音: %d", tone_id);
    
    // TODO: 实现提示音播放
    
    return ESP_OK;
}

/**
 * @brief 播放TTS语音
 */
esp_err_t audio_system_play_tts(const char *text)
{
    if (!text) {
        return ESP_ERR_INVALID_ARG;
    }
    
    ESP_LOGI(TAG, "播放TTS语音: %s", text);
    
    // TODO: 实现TTS语音合成和播放
    
    return ESP_OK;
}

/**
 * @brief 获取音频电平
 */
float audio_system_get_level(void)
{
    return voice_processor_get_level();
}

/**
 * @brief 检测语音活动
 */
bool audio_system_voice_detected(void)
{
    return voice_processor_voice_detected();
}

/**
 * @brief 启用/禁用音频效果
 */
esp_err_t audio_system_set_effect(uint8_t effect_id, bool enable)
{
    ESP_LOGI(TAG, "设置音频效果: %d, 启用=%d", effect_id, enable);
    
    return audio_effects_set_effect(effect_id, enable);
}
