/**
 * @file main.c
 * @brief TIMO智能闹钟主体设备主程序
 * @version 1.0.0
 * @date 2025-06-27
 * 
 * 主要功能：
 * - 系统初始化
 * - 硬件驱动初始化
 * - 任务创建和调度
 * - 系统监控
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_netif.h"
#include "esp_err.h"
#include "esp_timer.h"
#include "esp_sleep.h"
#include "esp_pm.h"

#include "app_main.h"

static const char *TAG = "TIMO_MAIN";

/**
 * @brief 系统初始化
 */
static void system_init(void)
{
    ESP_LOGI(TAG, "=== TIMO智能闹钟系统启动 ===");
    ESP_LOGI(TAG, "版本: %s", CONFIG_APP_PROJECT_VER);
    ESP_LOGI(TAG, "编译时间: %s %s", __DATE__, __TIME__);
    
    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    ESP_LOGI(TAG, "NVS初始化完成");
    
    // 初始化网络接口
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    ESP_LOGI(TAG, "网络接口初始化完成");
    
    // 初始化电源管理
    esp_pm_config_esp32s3_t pm_config = {
        .max_freq_mhz = 240,
        .min_freq_mhz = 80,
        .light_sleep_enable = true
    };
    ESP_ERROR_CHECK(esp_pm_configure(&pm_config));
    ESP_LOGI(TAG, "电源管理初始化完成");
}

/**
 * @brief 主程序入口
 */
void app_main(void)
{
    // 系统初始化
    system_init();
    
    // 启动应用程序
    app_main_start();
    
    ESP_LOGI(TAG, "系统启动完成，进入主循环");
    
    // 主循环 - 系统监控
    while (1) {
        // 打印系统状态
        ESP_LOGI(TAG, "系统运行中... 空闲堆内存: %d bytes", esp_get_free_heap_size());
        
        // 延时5秒
        vTaskDelay(pdMS_TO_TICKS(5000));
    }
}
