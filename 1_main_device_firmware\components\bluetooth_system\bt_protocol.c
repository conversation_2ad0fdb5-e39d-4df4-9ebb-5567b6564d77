/**
 * @file bt_protocol.c
 * @brief TIMO蓝牙通信协议实现
 * @version 1.0.0
 * @date 2025-06-28
 */

#include "bt_protocol.h"
#include "esp_log.h"
#include <string.h>

static const char *TAG = "BT_PROTOCOL";

/**
 * @brief 创建数据包
 */
uint16_t bt_protocol_create_packet(uint8_t type, uint8_t cmd, 
                                  const uint8_t *data, uint16_t data_len,
                                  uint8_t *packet, uint16_t packet_size)
{
    if (!packet || packet_size == 0) {
        ESP_LOGE(TAG, "无效的数据包缓冲区");
        return 0;
    }
    
    // 计算所需的数据包大小
    uint16_t required_size = sizeof(bt_packet_header_t) + data_len + 1 + 2; // +1校验和 +2包尾
    if (required_size > packet_size) {
        ESP_LOGE(TAG, "数据包缓冲区太小: 需要%d, 提供%d", required_size, packet_size);
        return 0;
    }
    
    // 填充包头
    bt_packet_header_t *header = (bt_packet_header_t*)packet;
    header->header = BT_PACKET_HEADER;
    header->version = BT_PROTOCOL_VERSION;
    header->type = type;
    header->cmd = cmd;
    header->length = data_len;
    
    uint16_t offset = sizeof(bt_packet_header_t);
    
    // 填充数据
    if (data && data_len > 0) {
        memcpy(packet + offset, data, data_len);
        offset += data_len;
    }
    
    // 计算校验和 (包头 + 数据)
    uint8_t checksum = bt_protocol_calculate_checksum(packet, offset);
    packet[offset] = checksum;
    offset++;
    
    // 填充包尾
    uint16_t footer = BT_PACKET_FOOTER;
    memcpy(packet + offset, &footer, sizeof(footer));
    offset += sizeof(footer);
    
    ESP_LOGD(TAG, "创建数据包: type=0x%02X, cmd=0x%02X, len=%d, total=%d", 
             type, cmd, data_len, offset);
    
    return offset;
}

/**
 * @brief 解析数据包
 */
bool bt_protocol_parse_packet(const uint8_t *packet, uint16_t packet_len,
                             bt_packet_header_t *header, 
                             const uint8_t **payload, uint16_t *payload_len)
{
    if (!packet || !header || !payload || !payload_len) {
        ESP_LOGE(TAG, "无效的参数");
        return false;
    }
    
    // 检查最小包长度
    if (packet_len < sizeof(bt_packet_header_t) + 1 + 2) { // +1校验和 +2包尾
        ESP_LOGE(TAG, "数据包太短: %d", packet_len);
        return false;
    }
    
    // 解析包头
    memcpy(header, packet, sizeof(bt_packet_header_t));
    
    // 验证包头标识
    if (header->header != BT_PACKET_HEADER) {
        ESP_LOGE(TAG, "无效的包头标识: 0x%04X", header->header);
        return false;
    }
    
    // 验证协议版本
    if (header->version != BT_PROTOCOL_VERSION) {
        ESP_LOGW(TAG, "协议版本不匹配: 期望0x%02X, 实际0x%02X", 
                 BT_PROTOCOL_VERSION, header->version);
    }
    
    // 检查数据长度
    uint16_t expected_len = sizeof(bt_packet_header_t) + header->length + 1 + 2;
    if (packet_len < expected_len) {
        ESP_LOGE(TAG, "数据包长度不匹配: 期望%d, 实际%d", expected_len, packet_len);
        return false;
    }
    
    // 验证包尾
    uint16_t footer_offset = sizeof(bt_packet_header_t) + header->length + 1;
    uint16_t footer;
    memcpy(&footer, packet + footer_offset, sizeof(footer));
    if (footer != BT_PACKET_FOOTER) {
        ESP_LOGE(TAG, "无效的包尾标识: 0x%04X", footer);
        return false;
    }
    
    // 验证校验和
    if (!bt_protocol_verify_checksum(packet, packet_len)) {
        ESP_LOGE(TAG, "校验和验证失败");
        return false;
    }
    
    // 设置载荷指针
    *payload = packet + sizeof(bt_packet_header_t);
    *payload_len = header->length;
    
    ESP_LOGD(TAG, "解析数据包成功: type=0x%02X, cmd=0x%02X, len=%d", 
             header->type, header->cmd, header->length);
    
    return true;
}

/**
 * @brief 计算校验和
 */
uint8_t bt_protocol_calculate_checksum(const uint8_t *data, uint16_t length)
{
    if (!data || length == 0) {
        return 0;
    }
    
    uint8_t checksum = 0;
    for (uint16_t i = 0; i < length; i++) {
        checksum ^= data[i];
    }
    
    return checksum;
}

/**
 * @brief 验证数据包校验和
 */
bool bt_protocol_verify_checksum(const uint8_t *packet, uint16_t packet_len)
{
    if (!packet || packet_len < sizeof(bt_packet_header_t) + 1 + 2) {
        return false;
    }
    
    // 获取包头信息
    bt_packet_header_t *header = (bt_packet_header_t*)packet;
    
    // 计算数据部分的校验和 (包头 + 载荷)
    uint16_t data_len = sizeof(bt_packet_header_t) + header->length;
    uint8_t calculated_checksum = bt_protocol_calculate_checksum(packet, data_len);
    
    // 获取数据包中的校验和
    uint8_t packet_checksum = packet[data_len];
    
    return (calculated_checksum == packet_checksum);
}

/**
 * @brief 创建心跳包
 */
uint16_t bt_protocol_create_ping_packet(uint8_t *packet, uint16_t packet_size)
{
    return bt_protocol_create_packet(BT_DATA_TYPE_CMD, BT_CMD_PING, NULL, 0, packet, packet_size);
}

/**
 * @brief 创建心跳响应包
 */
uint16_t bt_protocol_create_pong_packet(uint8_t *packet, uint16_t packet_size)
{
    return bt_protocol_create_packet(BT_DATA_TYPE_RESPONSE, BT_CMD_PONG, NULL, 0, packet, packet_size);
}

/**
 * @brief 创建LED场景控制包
 */
uint16_t bt_protocol_create_scene_packet(bt_scene_id_t scene_id, uint8_t brightness,
                                        uint8_t *packet, uint16_t packet_size)
{
    bt_scene_params_t params = {
        .scene_id = scene_id,
        .brightness = brightness,
        .duration_ms = 0,
        .color = {0, 0, 0, brightness}
    };
    
    return bt_protocol_create_packet(BT_DATA_TYPE_CMD, BT_CMD_LED_SET_SCENE, 
                                    (uint8_t*)&params, sizeof(params), 
                                    packet, packet_size);
}

/**
 * @brief 创建LED颜色控制包
 */
uint16_t bt_protocol_create_color_packet(uint8_t red, uint8_t green, uint8_t blue, 
                                        uint8_t brightness, uint8_t *packet, uint16_t packet_size)
{
    bt_led_color_t color = {
        .red = red,
        .green = green,
        .blue = blue,
        .brightness = brightness
    };
    
    return bt_protocol_create_packet(BT_DATA_TYPE_CMD, BT_CMD_LED_SET_COLOR, 
                                    (uint8_t*)&color, sizeof(color), 
                                    packet, packet_size);
}

/**
 * @brief 创建音频数据包
 */
uint16_t bt_protocol_create_audio_packet(bt_music_mode_t mode, uint8_t volume,
                                        const uint8_t *spectrum_data, 
                                        uint8_t *packet, uint16_t packet_size)
{
    bt_audio_data_t audio_data = {
        .mode = mode,
        .volume = volume,
        .frequency = 0
    };
    
    if (spectrum_data) {
        memcpy(audio_data.spectrum, spectrum_data, sizeof(audio_data.spectrum));
    }
    
    return bt_protocol_create_packet(BT_DATA_TYPE_CMD, BT_CMD_MUSIC_SEND_DATA, 
                                    (uint8_t*)&audio_data, sizeof(audio_data), 
                                    packet, packet_size);
}

/**
 * @brief 创建状态查询包
 */
uint16_t bt_protocol_create_status_query_packet(uint8_t *packet, uint16_t packet_size)
{
    return bt_protocol_create_packet(BT_DATA_TYPE_CMD, BT_CMD_GET_STATUS, NULL, 0, packet, packet_size);
}

/**
 * @brief 解析设备状态响应
 */
bool bt_protocol_parse_status_response(const uint8_t *payload, uint16_t payload_len,
                                      bt_device_status_t *status)
{
    if (!payload || !status || payload_len < sizeof(bt_device_status_t)) {
        return false;
    }
    
    memcpy(status, payload, sizeof(bt_device_status_t));
    return true;
}

/**
 * @brief 解析版本信息响应
 */
bool bt_protocol_parse_version_response(const uint8_t *payload, uint16_t payload_len,
                                       bt_version_info_t *version)
{
    if (!payload || !version || payload_len < sizeof(bt_version_info_t)) {
        return false;
    }
    
    memcpy(version, payload, sizeof(bt_version_info_t));
    return true;
}
