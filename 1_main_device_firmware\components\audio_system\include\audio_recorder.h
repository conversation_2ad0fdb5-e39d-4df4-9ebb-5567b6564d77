/**
 * @file audio_recorder.h
 * @brief TIMO音频录制器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef AUDIO_RECORDER_H
#define AUDIO_RECORDER_H

#include "esp_err.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

esp_err_t audio_recorder_init(void);
esp_err_t audio_recorder_start(void);
esp_err_t audio_recorder_stop(void);
esp_err_t audio_recorder_start_record(const char *file_path, uint32_t duration_ms);
esp_err_t audio_recorder_stop_record(void);

#ifdef __cplusplus
}
#endif

#endif /* AUDIO_RECORDER_H */
