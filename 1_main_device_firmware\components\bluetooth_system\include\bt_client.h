/**
 * @file bt_client.h
 * @brief TIMO蓝牙客户端头文件
 * @version 1.0.0
 * @date 2025-06-28
 */

#ifndef BT_CLIENT_H
#define BT_CLIENT_H

#include "esp_err.h"
#include "esp_bt.h"
#include "esp_gap_ble_api.h"
#include "esp_gattc_api.h"
#include "bluetooth_system.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* GATT客户端应用ID */
#define BT_CLIENT_APP_ID            0

/* 扫描参数 */
#define BT_SCAN_INTERVAL            0x50    // 50ms
#define BT_SCAN_WINDOW              0x30    // 30ms
#define BT_SCAN_FILTER_POLICY       BLE_SCAN_FILTER_ALLOW_ALL
#define BT_SCAN_TYPE                BLE_SCAN_TYPE_ACTIVE

/* 连接参数 */
#define BT_CONN_INTERVAL_MIN        0x10    // 20ms
#define BT_CONN_INTERVAL_MAX        0x20    // 40ms
#define BT_CONN_LATENCY             0
#define BT_CONN_TIMEOUT             400     // 4s

/* 客户端状态 */
typedef enum {
    BT_CLIENT_STATE_IDLE = 0,
    BT_CLIENT_STATE_SCANNING,
    BT_CLIENT_STATE_SCAN_COMPLETE,
    BT_CLIENT_STATE_CONNECTING,
    BT_CLIENT_STATE_CONNECTED,
    BT_CLIENT_STATE_DISCOVERING,
    BT_CLIENT_STATE_READY,
    BT_CLIENT_STATE_DISCONNECTED,
    BT_CLIENT_STATE_ERROR
} bt_client_state_t;

/* 服务发现状态 */
typedef struct {
    bool service_found;                 // 服务是否找到
    bool char_cmd_found;                // 命令特征值是否找到
    bool char_data_found;               // 数据特征值是否找到
    uint16_t service_start_handle;      // 服务起始句柄
    uint16_t service_end_handle;        // 服务结束句柄
    uint16_t char_cmd_handle;           // 命令特征值句柄
    uint16_t char_data_handle;          // 数据特征值句柄
    uint16_t char_cmd_desc_handle;      // 命令特征值描述符句柄
    uint16_t char_data_desc_handle;     // 数据特征值描述符句柄
} bt_service_discovery_t;

/* 客户端回调函数类型 */
typedef void (*bt_client_event_cb_t)(bt_event_type_t event, void *data);
typedef void (*bt_client_data_cb_t)(const uint8_t *data, uint16_t length);

/**
 * @brief 初始化BLE客户端
 * @return esp_err_t 
 */
esp_err_t bt_client_init(void);

/**
 * @brief 反初始化BLE客户端
 * @return esp_err_t 
 */
esp_err_t bt_client_deinit(void);

/**
 * @brief 开始扫描设备
 * @param timeout_s 扫描超时时间(秒)
 * @return esp_err_t 
 */
esp_err_t bt_client_start_scan(uint32_t timeout_s);

/**
 * @brief 停止扫描
 * @return esp_err_t 
 */
esp_err_t bt_client_stop_scan(void);

/**
 * @brief 连接到指定设备
 * @param remote_addr 远程设备地址
 * @return esp_err_t 
 */
esp_err_t bt_client_connect(const esp_bd_addr_t remote_addr);

/**
 * @brief 断开连接
 * @return esp_err_t 
 */
esp_err_t bt_client_disconnect(void);

/**
 * @brief 发现服务
 * @return esp_err_t 
 */
esp_err_t bt_client_discover_services(void);

/**
 * @brief 写入特征值
 * @param char_handle 特征值句柄
 * @param data 数据
 * @param length 数据长度
 * @return esp_err_t 
 */
esp_err_t bt_client_write_char(uint16_t char_handle, const uint8_t *data, uint16_t length);

/**
 * @brief 读取特征值
 * @param char_handle 特征值句柄
 * @return esp_err_t 
 */
esp_err_t bt_client_read_char(uint16_t char_handle);

/**
 * @brief 启用特征值通知
 * @param char_handle 特征值句柄
 * @param enable 是否启用
 * @return esp_err_t 
 */
esp_err_t bt_client_enable_notification(uint16_t char_handle, bool enable);

/**
 * @brief 获取客户端状态
 * @return bt_client_state_t 
 */
bt_client_state_t bt_client_get_state(void);

/**
 * @brief 获取连接信息
 * @return const bt_connection_info_t* 
 */
const bt_connection_info_t* bt_client_get_connection_info(void);

/**
 * @brief 获取服务发现信息
 * @return const bt_service_discovery_t* 
 */
const bt_service_discovery_t* bt_client_get_service_info(void);

/**
 * @brief 检查是否已连接并准备就绪
 * @return true 已连接且服务发现完成
 * @return false 未连接或服务未发现
 */
bool bt_client_is_ready(void);

/**
 * @brief 注册事件回调函数
 * @param callback 回调函数
 * @return esp_err_t 
 */
esp_err_t bt_client_register_event_callback(bt_client_event_cb_t callback);

/**
 * @brief 注册数据接收回调函数
 * @param callback 回调函数
 * @return esp_err_t 
 */
esp_err_t bt_client_register_data_callback(bt_client_data_cb_t callback);

/**
 * @brief 获取扫描结果
 * @param devices 设备列表缓冲区
 * @param max_count 最大设备数量
 * @return uint8_t 实际设备数量
 */
uint8_t bt_client_get_scan_results(bt_device_info_t *devices, uint8_t max_count);

/**
 * @brief 清除扫描结果
 */
void bt_client_clear_scan_results(void);

/**
 * @brief 设置扫描参数
 * @param interval 扫描间隔
 * @param window 扫描窗口
 * @return esp_err_t 
 */
esp_err_t bt_client_set_scan_params(uint16_t interval, uint16_t window);

/**
 * @brief 设置连接参数
 * @param interval_min 最小连接间隔
 * @param interval_max 最大连接间隔
 * @param latency 连接延迟
 * @param timeout 超时时间
 * @return esp_err_t 
 */
esp_err_t bt_client_set_conn_params(uint16_t interval_min, uint16_t interval_max,
                                   uint16_t latency, uint16_t timeout);

/**
 * @brief 获取连接的RSSI值
 * @return int8_t RSSI值
 */
int8_t bt_client_get_rssi(void);

/**
 * @brief 更新连接参数
 * @return esp_err_t 
 */
esp_err_t bt_client_update_conn_params(void);

#ifdef __cplusplus
}
#endif

#endif /* BT_CLIENT_H */
