# TIMO智能闹钟云端服务

## 项目简介

TIMO智能闹钟的云端服务，提供设备管理、数据同步、API服务等功能。

## 技术栈

- **后端框架**: Node.js + Express
- **数据库**: MongoDB
- **认证**: JWT
- **实时通信**: WebSocket
- **部署**: Docker + PM2

## 主要功能

### 1. 设备管理
- 设备注册和认证
- 设备状态监控
- 设备配置管理
- 固件OTA更新

### 2. 用户管理
- 用户注册和登录
- 用户信息管理
- 权限控制

### 3. 数据服务
- 传感器数据存储
- 历史数据查询
- 数据统计分析
- 数据导出

### 4. 语音服务
- 语音识别
- 语音合成
- 智能对话
- 语音指令处理

### 5. 通知服务
- 消息推送
- 邮件通知
- 短信通知
- 微信通知

## API接口

### 设备相关
- `POST /api/device/register` - 设备注册
- `POST /api/device/auth` - 设备认证
- `GET /api/device/status` - 获取设备状态
- `PUT /api/device/config` - 更新设备配置

### 数据相关
- `POST /api/data/sensor` - 上传传感器数据
- `GET /api/data/history` - 获取历史数据
- `GET /api/data/statistics` - 获取统计数据

### 用户相关
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `GET /api/user/profile` - 获取用户信息
- `PUT /api/user/profile` - 更新用户信息

## 部署说明

### 环境要求
- Node.js >= 16.0
- MongoDB >= 4.4
- Redis >= 6.0

### 安装依赖
```bash
npm install
```

### 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

### 启动服务
```bash
# 开发环境
npm run dev

# 生产环境
npm run start
```

### Docker部署
```bash
docker-compose up -d
```

## 开发指南

### 目录结构
```
src/
├── app.js              # 应用入口
├── routes/             # 路由定义
├── controllers/        # 控制器
├── models/             # 数据模型
├── middleware/         # 中间件
├── services/           # 业务服务
├── utils/              # 工具函数
└── config/             # 配置文件
```

### 开发规范
- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- 编写单元测试
- 遵循RESTful API设计规范

## 监控和日志

### 日志管理
- 使用Winston进行日志记录
- 日志分级：error, warn, info, debug
- 日志轮转和归档

### 性能监控
- API响应时间监控
- 数据库查询性能监控
- 内存和CPU使用率监控

### 错误处理
- 全局错误处理中间件
- 错误日志记录
- 错误报警机制

## 安全措施

### 认证和授权
- JWT Token认证
- API访问频率限制
- 设备白名单机制

### 数据安全
- 数据传输加密(HTTPS)
- 敏感数据加密存储
- 数据备份和恢复

### 网络安全
- 防火墙配置
- DDoS攻击防护
- SQL注入防护
