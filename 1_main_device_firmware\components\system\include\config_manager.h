/**
 * @file config_manager.h
 * @brief TIMO配置管理器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef CONFIG_MANAGER_H
#define CONFIG_MANAGER_H

#include "esp_err.h"
#include "system_manager.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化配置管理器
 * @return esp_err_t 
 */
esp_err_t config_manager_init(void);

/**
 * @brief 加载系统配置
 * @param config 配置结构体指针
 * @return esp_err_t 
 */
esp_err_t config_manager_load_system_config(system_config_t *config);

/**
 * @brief 保存系统配置
 * @param config 配置结构体指针
 * @return esp_err_t 
 */
esp_err_t config_manager_save_system_config(const system_config_t *config);

/**
 * @brief 重置配置为默认值
 * @return esp_err_t 
 */
esp_err_t config_manager_reset_to_default(void);

#ifdef __cplusplus
}
#endif

#endif /* CONFIG_MANAGER_H */
