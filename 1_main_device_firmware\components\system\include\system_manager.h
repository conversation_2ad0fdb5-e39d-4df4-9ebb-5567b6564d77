/**
 * @file system_manager.h
 * @brief TIMO系统管理器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef SYSTEM_MANAGER_H
#define SYSTEM_MANAGER_H

#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 系统状态定义 */
typedef enum {
    SYSTEM_STATE_INIT = 0,          // 初始化状态
    SYSTEM_STATE_STARTING,          // 启动中
    SYSTEM_STATE_RUNNING,           // 正常运行
    SYSTEM_STATE_SLEEP,             // 睡眠状态
    SYSTEM_STATE_CHARGING,          // 充电状态
    SYSTEM_STATE_ERROR,             // 错误状态
    SYSTEM_STATE_SHUTDOWN,          // 关机状态
    SYSTEM_STATE_MAX
} system_state_t;

/* 系统事件定义 */
typedef enum {
    SYSTEM_EVENT_STARTUP = 0,       // 系统启动
    SYSTEM_EVENT_SHUTDOWN,          // 系统关机
    SYSTEM_EVENT_SLEEP,             // 进入睡眠
    SYSTEM_EVENT_WAKEUP,            // 唤醒
    SYSTEM_EVENT_CHARGING_START,    // 开始充电
    SYSTEM_EVENT_CHARGING_STOP,     // 停止充电
    SYSTEM_EVENT_LOW_BATTERY,       // 低电量
    SYSTEM_EVENT_CRITICAL_BATTERY,  // 电量严重不足
    SYSTEM_EVENT_ERROR,             // 系统错误
    SYSTEM_EVENT_FACTORY_RESET,     // 恢复出厂设置
    SYSTEM_EVENT_MAX
} system_event_t;

/* 系统配置结构体 */
typedef struct {
    uint8_t brightness;             // 屏幕亮度 (0-100)
    uint8_t volume;                 // 音量 (0-100)
    bool auto_brightness;           // 自动亮度
    bool sleep_mode_enabled;        // 睡眠模式
    uint32_t sleep_timeout_ms;      // 睡眠超时时间
    bool wifi_enabled;              // WiFi开关
    bool bluetooth_enabled;         // 蓝牙开关
    char timezone[32];              // 时区设置
    char language[8];               // 语言设置
} system_config_t;

/* 系统状态结构体 */
typedef struct {
    system_state_t state;           // 当前状态
    uint64_t uptime_ms;             // 运行时间
    uint8_t battery_level;          // 电池电量
    bool charging;                  // 充电状态
    bool wifi_connected;            // WiFi连接状态
    bool bluetooth_connected;       // 蓝牙连接状态
    uint32_t free_heap;             // 空闲堆内存
    uint32_t min_free_heap;         // 最小空闲堆内存
    float cpu_usage;                // CPU使用率
    int8_t rssi;                    // WiFi信号强度
} system_status_t;

/* 系统事件回调函数类型 */
typedef void (*system_event_callback_t)(system_event_t event, void *data);

/**
 * @brief 初始化系统管理器
 * @return esp_err_t 
 */
esp_err_t system_manager_init(void);

/**
 * @brief 启动系统管理器
 * @return esp_err_t 
 */
esp_err_t system_manager_start(void);

/**
 * @brief 停止系统管理器
 * @return esp_err_t 
 */
esp_err_t system_manager_stop(void);

/**
 * @brief 获取系统状态
 * @return system_status_t* 
 */
system_status_t* system_manager_get_status(void);

/**
 * @brief 获取系统配置
 * @return system_config_t* 
 */
system_config_t* system_manager_get_config(void);

/**
 * @brief 设置系统配置
 * @param config 配置结构体
 * @return esp_err_t 
 */
esp_err_t system_manager_set_config(const system_config_t *config);

/**
 * @brief 保存系统配置
 * @return esp_err_t 
 */
esp_err_t system_manager_save_config(void);

/**
 * @brief 注册系统事件回调
 * @param callback 回调函数
 * @return esp_err_t 
 */
esp_err_t system_manager_register_event_callback(system_event_callback_t callback);

/**
 * @brief 发送系统事件
 * @param event 事件类型
 * @param data 事件数据
 * @return esp_err_t 
 */
esp_err_t system_manager_send_event(system_event_t event, void *data);

/**
 * @brief 系统重启
 */
void system_manager_restart(void);

/**
 * @brief 系统关机
 */
void system_manager_shutdown(void);

/**
 * @brief 进入睡眠模式
 * @param sleep_time_ms 睡眠时间(毫秒)，0表示无限睡眠
 */
void system_manager_sleep(uint32_t sleep_time_ms);

/**
 * @brief 恢复出厂设置
 * @return esp_err_t 
 */
esp_err_t system_manager_factory_reset(void);

/**
 * @brief 获取系统运行时间
 * @return uint64_t 运行时间(毫秒)
 */
uint64_t system_manager_get_uptime(void);

/**
 * @brief 获取系统版本信息
 * @return const char* 版本字符串
 */
const char* system_manager_get_version(void);

#ifdef __cplusplus
}
#endif

#endif /* SYSTEM_MANAGER_H */
