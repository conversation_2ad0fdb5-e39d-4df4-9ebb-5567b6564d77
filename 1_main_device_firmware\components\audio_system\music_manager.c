/**
 * @file music_manager.c
 * @brief TIMO音乐管理器实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "music_manager.h"
#include "esp_log.h"

static const char *TAG = "MUSIC_MANAGER";

esp_err_t music_manager_init(void)
{
    ESP_LOGI(TAG, "初始化音乐管理器...");
    return ESP_OK;
}

esp_err_t music_manager_start(void)
{
    ESP_LOGI(TAG, "启动音乐管理器...");
    return ESP_OK;
}

esp_err_t music_manager_stop(void)
{
    ESP_LOGI(TAG, "停止音乐管理器...");
    return ESP_OK;
}
