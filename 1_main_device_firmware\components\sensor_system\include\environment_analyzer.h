/**
 * @file environment_analyzer.h
 * @brief TIMO环境分析器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef ENVIRONMENT_ANALYZER_H
#define ENVIRONMENT_ANALYZER_H

#include "esp_err.h"
#include "sensor_system.h"

#ifdef __cplusplus
extern "C" {
#endif

esp_err_t environment_analyzer_init(void);
esp_err_t environment_analyzer_start(void);
esp_err_t environment_analyzer_stop(void);
environment_status_t environment_analyzer_analyze(const environment_data_t *data);
uint8_t environment_analyzer_get_comfort_score(const environment_data_t *data);
uint16_t environment_analyzer_get_aqi(const environment_data_t *data);
bool environment_analyzer_need_ventilation(const environment_data_t *data);
esp_err_t environment_analyzer_get_suggestions(const environment_data_t *data, char *buffer, size_t size);

#ifdef __cplusplus
}
#endif

#endif /* ENVIRONMENT_ANALYZER_H */
