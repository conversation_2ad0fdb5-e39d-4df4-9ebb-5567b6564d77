/**
 * @file audio_player.h
 * @brief TIMO音频播放器头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef AUDIO_PLAYER_H
#define AUDIO_PLAYER_H

#include "esp_err.h"
#include "audio_system.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

esp_err_t audio_player_init(void);
esp_err_t audio_player_start(void);
esp_err_t audio_player_stop(void);
esp_err_t audio_player_play_file(const char *file_path);
esp_err_t audio_player_play_data(const uint8_t *data, size_t size, audio_format_t format);
esp_err_t audio_player_pause(void);
esp_err_t audio_player_resume(void);

#ifdef __cplusplus
}
#endif

#endif /* AUDIO_PLAYER_H */
