/**
 * @file hardware_hal.h
 * @brief TIMO智能闹钟硬件抽象层接口
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef HARDWARE_HAL_H
#define HARDWARE_HAL_H

#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ========== 硬件初始化接口 ========== */

/**
 * @brief 初始化所有硬件组件
 * @return esp_err_t 
 */
esp_err_t hardware_init_all(void);

/**
 * @brief 反初始化所有硬件组件
 * @return esp_err_t 
 */
esp_err_t hardware_deinit_all(void);

/* ========== I2C总线接口 ========== */

/**
 * @brief I2C写数据
 * @param device_addr 设备地址
 * @param reg_addr 寄存器地址
 * @param data 数据缓冲区
 * @param len 数据长度
 * @return esp_err_t 
 */
esp_err_t i2c_write_bytes(uint8_t device_addr, uint8_t reg_addr, uint8_t *data, size_t len);

/**
 * @brief I2C读数据
 * @param device_addr 设备地址
 * @param reg_addr 寄存器地址
 * @param data 数据缓冲区
 * @param len 数据长度
 * @return esp_err_t 
 */
esp_err_t i2c_read_bytes(uint8_t device_addr, uint8_t reg_addr, uint8_t *data, size_t len);

/**
 * @brief I2C扫描设备
 * @param found_devices 找到的设备地址数组
 * @param max_devices 最大设备数量
 * @return int 找到的设备数量
 */
int i2c_scan_devices(uint8_t *found_devices, int max_devices);

/* ========== SPI总线接口 ========== */

/**
 * @brief SPI传输数据
 * @param cs_pin 片选引脚（-1表示使用扩展IO）
 * @param tx_data 发送数据
 * @param rx_data 接收数据
 * @param len 数据长度
 * @return esp_err_t 
 */
esp_err_t spi_transfer(int cs_pin, uint8_t *tx_data, uint8_t *rx_data, size_t len);

/* ========== LCD显示接口 ========== */

/**
 * @brief LCD初始化
 * @return esp_err_t 
 */
esp_err_t lcd_init(void);

/**
 * @brief LCD设置背光亮度
 * @param brightness 亮度值 (0-100)
 * @return esp_err_t 
 */
esp_err_t lcd_set_backlight(uint8_t brightness);

/**
 * @brief LCD显示缓冲区
 * @param x 起始X坐标
 * @param y 起始Y坐标
 * @param width 宽度
 * @param height 高度
 * @param color_data 颜色数据
 * @return esp_err_t 
 */
esp_err_t lcd_draw_bitmap(int x, int y, int width, int height, uint16_t *color_data);

/* ========== 触摸屏接口 ========== */

typedef struct {
    int x;
    int y;
    bool pressed;
} touch_point_t;

/**
 * @brief 触摸屏初始化
 * @return esp_err_t 
 */
esp_err_t touch_init(void);

/**
 * @brief 读取触摸点
 * @param point 触摸点数据
 * @return esp_err_t 
 */
esp_err_t touch_read_point(touch_point_t *point);

/* ========== 传感器接口 ========== */

typedef struct {
    float temperature;  // 温度 (°C)
    float humidity;     // 湿度 (%)
} aht30_data_t;

typedef struct {
    float lux;          // 光照强度 (lux)
} bh1750_data_t;

typedef struct {
    uint16_t co2;       // CO2浓度 (ppm)
    uint16_t tvoc;      // TVOC浓度 (ppb)
} sgp30_data_t;

typedef struct {
    float acc_x, acc_y, acc_z;      // 加速度 (g)
    float gyro_x, gyro_y, gyro_z;   // 角速度 (dps)
} qmi8658_data_t;

/**
 * @brief 初始化所有传感器
 * @return esp_err_t 
 */
esp_err_t sensors_init(void);

/**
 * @brief 读取温湿度传感器
 * @param data 传感器数据
 * @return esp_err_t 
 */
esp_err_t aht30_read(aht30_data_t *data);

/**
 * @brief 读取光照传感器
 * @param data 传感器数据
 * @return esp_err_t 
 */
esp_err_t bh1750_read(bh1750_data_t *data);

/**
 * @brief 读取CO2传感器
 * @param data 传感器数据
 * @return esp_err_t 
 */
esp_err_t sgp30_read(sgp30_data_t *data);

/**
 * @brief 读取姿态传感器
 * @param data 传感器数据
 * @return esp_err_t 
 */
esp_err_t qmi8658_read(qmi8658_data_t *data);

/* ========== 音频接口 ========== */

/**
 * @brief 音频系统初始化
 * @return esp_err_t 
 */
esp_err_t audio_init(void);

/**
 * @brief 开始录音
 * @param sample_rate 采样率
 * @param bits_per_sample 采样位数
 * @return esp_err_t 
 */
esp_err_t audio_start_record(uint32_t sample_rate, uint8_t bits_per_sample);

/**
 * @brief 停止录音
 * @return esp_err_t 
 */
esp_err_t audio_stop_record(void);

/**
 * @brief 开始播放
 * @param sample_rate 采样率
 * @param bits_per_sample 采样位数
 * @return esp_err_t 
 */
esp_err_t audio_start_play(uint32_t sample_rate, uint8_t bits_per_sample);

/**
 * @brief 停止播放
 * @return esp_err_t 
 */
esp_err_t audio_stop_play(void);

/**
 * @brief 设置音量
 * @param volume 音量 (0-100)
 * @return esp_err_t 
 */
esp_err_t audio_set_volume(uint8_t volume);

/* ========== RTC时钟接口 ========== */

typedef struct {
    uint8_t year;       // 年 (0-99, 表示2000-2099)
    uint8_t month;      // 月 (1-12)
    uint8_t day;        // 日 (1-31)
    uint8_t hour;       // 时 (0-23)
    uint8_t minute;     // 分 (0-59)
    uint8_t second;     // 秒 (0-59)
    uint8_t weekday;    // 星期 (0-6, 0=星期日)
} rtc_time_t;

/**
 * @brief RTC初始化
 * @return esp_err_t 
 */
esp_err_t rtc_init(void);

/**
 * @brief 设置RTC时间
 * @param time 时间结构体
 * @return esp_err_t 
 */
esp_err_t rtc_set_time(rtc_time_t *time);

/**
 * @brief 读取RTC时间
 * @param time 时间结构体
 * @return esp_err_t 
 */
esp_err_t rtc_get_time(rtc_time_t *time);

/* ========== SD卡接口 ========== */

/**
 * @brief SD卡初始化
 * @return esp_err_t 
 */
esp_err_t sd_card_init(void);

/**
 * @brief SD卡反初始化
 * @return esp_err_t 
 */
esp_err_t sd_card_deinit(void);

/**
 * @brief 检查SD卡是否插入
 * @return bool true=已插入, false=未插入
 */
bool sd_card_is_mounted(void);

/* ========== 电池监控接口 ========== */

/**
 * @brief 电池监控初始化
 * @return esp_err_t 
 */
esp_err_t battery_monitor_init(void);

/**
 * @brief 读取电池电压
 * @return float 电池电压 (V)
 */
float battery_get_voltage(void);

/**
 * @brief 获取电池电量百分比
 * @return uint8_t 电量百分比 (0-100)
 */
uint8_t battery_get_percentage(void);

#ifdef __cplusplus
}
#endif

#endif /* HARDWARE_HAL_H */
